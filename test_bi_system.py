#!/usr/bin/env python3
"""
🧪 SCRIPT DE TEST AUTOMATIQUE DU SYSTÈME BI
Teste toutes les fonctionnalités BI automatiquement
"""

import requests
import json
import time
import sys
from pathlib import Path

class BISystemTester:
    def __init__(self):
        self.base_url = "http://localhost:8000"
        self.test_results = []
        
    def print_test(self, test_name, status, message=""):
        """Affiche le résultat d'un test"""
        status_icon = "✅" if status else "❌"
        print(f"{status_icon} {test_name}: {message}")
        self.test_results.append({"test": test_name, "status": status, "message": message})
    
    def test_server_health(self):
        """Test de santé du serveur"""
        try:
            response = requests.get(f"{self.base_url}/health", timeout=5)
            if response.status_code == 200:
                self.print_test("Santé du serveur", True, "Serveur opérationnel")
                return True
            else:
                self.print_test("<PERSON><PERSON> du serveur", False, f"Code: {response.status_code}")
                return False
        except Exception as e:
            self.print_test("Santé du serveur", False, f"Erreur: {str(e)}")
            return False
    
    def test_bi_status(self):
        """Test du statut BI"""
        try:
            response = requests.get(f"{self.base_url}/bi/status", timeout=5)
            if response.status_code == 200:
                data = response.json()
                active_connectors = data.get('active_connectors', 0)
                total_connectors = data.get('total_connectors', 0)
                self.print_test("Statut BI", True, f"{active_connectors}/{total_connectors} connecteurs actifs")
                return True
            else:
                self.print_test("Statut BI", False, f"Code: {response.status_code}")
                return False
        except Exception as e:
            self.print_test("Statut BI", False, f"Erreur: {str(e)}")
            return False
    
    def test_powerbi_export(self):
        """Test d'export Power BI"""
        try:
            data = {"project_id": "Test1"}
            response = requests.post(f"{self.base_url}/bi/export-powerbi", data=data, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    self.print_test("Export Power BI", True, "Export simulé réussi")
                else:
                    self.print_test("Export Power BI", False, result.get('message', 'Erreur inconnue'))
            else:
                self.print_test("Export Power BI", False, f"Code: {response.status_code}")
        except Exception as e:
            self.print_test("Export Power BI", False, f"Erreur: {str(e)}")
    
    def test_tableau_export(self):
        """Test d'export Tableau"""
        try:
            data = {"project_id": "Test1"}
            response = requests.post(f"{self.base_url}/bi/export-tableau", data=data, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    self.print_test("Export Tableau", True, "Export simulé réussi")
                else:
                    self.print_test("Export Tableau", False, result.get('message', 'Erreur inconnue'))
            else:
                self.print_test("Export Tableau", False, f"Code: {response.status_code}")
        except Exception as e:
            self.print_test("Export Tableau", False, f"Erreur: {str(e)}")
    
    def test_n8n_workflow(self):
        """Test de workflow n8n"""
        try:
            data = {"project_id": "Test1", "workflow_type": "test"}
            response = requests.post(f"{self.base_url}/bi/trigger-n8n-workflow", data=data, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    self.print_test("Workflow n8n", True, "Workflow déclenché")
                else:
                    self.print_test("Workflow n8n", False, result.get('message', 'Erreur inconnue'))
            else:
                self.print_test("Workflow n8n", False, f"Code: {response.status_code}")
        except Exception as e:
            self.print_test("Workflow n8n", False, f"Erreur: {str(e)}")
    
    def test_erp_sync(self):
        """Test de synchronisation ERP"""
        try:
            data = {"project_id": "Test1"}
            response = requests.post(f"{self.base_url}/bi/sync-erp", data=data, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    self.print_test("Sync ERP", True, "Synchronisation simulée réussie")
                else:
                    self.print_test("Sync ERP", False, result.get('message', 'Erreur inconnue'))
            else:
                self.print_test("Sync ERP", False, f"Code: {response.status_code}")
        except Exception as e:
            self.print_test("Sync ERP", False, f"Erreur: {str(e)}")
    
    def test_multi_platform_export(self):
        """Test d'export multi-plateformes"""
        try:
            data = {"project_id": "Test1"}
            response = requests.post(f"{self.base_url}/bi/export-all-platforms", data=data, timeout=15)
            
            if response.status_code == 200:
                result = response.json()
                success_rate = result.get('summary', {}).get('success_rate', 0)
                self.print_test("Export Multi-Plateformes", True, f"Taux de succès: {success_rate}%")
            else:
                self.print_test("Export Multi-Plateformes", False, f"Code: {response.status_code}")
        except Exception as e:
            self.print_test("Export Multi-Plateformes", False, f"Erreur: {str(e)}")
    
    def test_sync_history(self):
        """Test de l'historique des synchronisations"""
        try:
            response = requests.get(f"{self.base_url}/bi/sync-history?limit=10", timeout=5)
            
            if response.status_code == 200:
                result = response.json()
                total_syncs = result.get('statistics', {}).get('total_synchronizations', 0)
                self.print_test("Historique BI", True, f"{total_syncs} synchronisations enregistrées")
            else:
                self.print_test("Historique BI", False, f"Code: {response.status_code}")
        except Exception as e:
            self.print_test("Historique BI", False, f"Erreur: {str(e)}")
    
    def run_all_tests(self):
        """Lance tous les tests"""
        print("🧪 TESTS AUTOMATIQUES DU SYSTÈME BI BIMEX")
        print("=" * 60)
        
        # Test de base
        if not self.test_server_health():
            print("\n❌ ERREUR CRITIQUE: Serveur non accessible")
            print("Assurez-vous que le serveur est démarré avec: python start_bimex_bi.py")
            return False
        
        print("\n📡 Tests des APIs BI...")
        self.test_bi_status()
        
        print("\n🔗 Tests des connecteurs...")
        self.test_powerbi_export()
        self.test_tableau_export()
        self.test_n8n_workflow()
        self.test_erp_sync()
        
        print("\n🚀 Tests des fonctionnalités avancées...")
        self.test_multi_platform_export()
        self.test_sync_history()
        
        # Résumé
        total_tests = len(self.test_results)
        passed_tests = sum(1 for test in self.test_results if test['status'])
        success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
        
        print("\n" + "=" * 60)
        print("📊 RÉSUMÉ DES TESTS")
        print("=" * 60)
        print(f"✅ Tests réussis: {passed_tests}/{total_tests}")
        print(f"📈 Taux de succès: {success_rate:.1f}%")
        
        if success_rate >= 80:
            print("\n🎉 SYSTÈME BI OPÉRATIONNEL !")
            print("Vous pouvez utiliser toutes les fonctionnalités BI.")
        elif success_rate >= 50:
            print("\n⚠️ SYSTÈME PARTIELLEMENT OPÉRATIONNEL")
            print("Certaines fonctionnalités peuvent ne pas fonctionner.")
        else:
            print("\n❌ PROBLÈMES DÉTECTÉS")
            print("Vérifiez la configuration et les logs du serveur.")
        
        return success_rate >= 80

def main():
    """Fonction principale"""
    print("Vérification que le serveur est accessible...")
    
    # Attendre un peu si le serveur vient d'être démarré
    time.sleep(2)
    
    tester = BISystemTester()
    success = tester.run_all_tests()
    
    if success:
        print("\n🌐 Interface disponible sur: http://localhost:8000/app/home.html")
        print("📊 Cliquez sur le bouton BI flottant pour accéder au dashboard")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
