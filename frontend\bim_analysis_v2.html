<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BIMEX 2.0 - Mission Control Interface</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        :root {
            /* Couleurs futuristes */
            --primary-neon: #00f5ff;
            --secondary-neon: #ff0080;
            --success-neon: #00ff88;
            --warning-neon: #ffaa00;
            --danger-neon: #ff3366;
            --purple-neon: #8b5cf6;
            
            /* Arrière-plans sombres */
            --bg-dark: #0a0a0f;
            --bg-card: #1a1a2e;
            --bg-glass: rgba(26, 26, 46, 0.8);
            --bg-hover: rgba(0, 245, 255, 0.1);
            
            /* Texte */
            --text-primary: #ffffff;
            --text-secondary: #a0a0b0;
            --text-muted: #606070;
            
            /* Effets */
            --glow-primary: 0 0 20px rgba(0, 245, 255, 0.5);
            --glow-secondary: 0 0 20px rgba(255, 0, 128, 0.5);
            --glow-success: 0 0 20px rgba(0, 255, 136, 0.5);
            
            /* Animations */
            --transition-fast: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            --transition-smooth: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: var(--bg-dark);
            color: var(--text-primary);
            overflow-x: hidden;
            position: relative;
        }

        /* Arrière-plan animé futuriste */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(circle at 20% 80%, rgba(0, 245, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 0, 128, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(139, 92, 246, 0.1) 0%, transparent 50%);
            z-index: -2;
            animation: backgroundPulse 8s ease-in-out infinite;
        }

        @keyframes backgroundPulse {
            0%, 100% { opacity: 0.3; }
            50% { opacity: 0.6; }
        }

        /* Grille de fond style Matrix/Cyberpunk */
        body::after {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: 
                linear-gradient(rgba(0, 245, 255, 0.03) 1px, transparent 1px),
                linear-gradient(90deg, rgba(0, 245, 255, 0.03) 1px, transparent 1px);
            background-size: 50px 50px;
            z-index: -1;
            animation: gridMove 20s linear infinite;
        }

        @keyframes gridMove {
            0% { transform: translate(0, 0); }
            100% { transform: translate(50px, 50px); }
        }

        /* Container principal - Style Mission Control */
        .mission-control {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            position: relative;
        }

        /* Header futuriste */
        .control-header {
            background: linear-gradient(135deg, var(--bg-card) 0%, rgba(0, 245, 255, 0.1) 100%);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(0, 245, 255, 0.2);
            padding: 20px 40px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logo-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, var(--primary-neon), var(--secondary-neon));
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            box-shadow: var(--glow-primary);
            animation: logoGlow 3s ease-in-out infinite;
        }

        @keyframes logoGlow {
            0%, 100% { box-shadow: var(--glow-primary); }
            50% { box-shadow: var(--glow-secondary); }
        }

        .logo-text {
            display: flex;
            flex-direction: column;
        }

        .logo-title {
            font-size: 28px;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-neon), var(--secondary-neon));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .logo-subtitle {
            font-size: 12px;
            color: var(--text-secondary);
            font-family: 'JetBrains Mono', monospace;
            letter-spacing: 2px;
            text-transform: uppercase;
        }

        /* Status système en temps réel */
        .system-status {
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            background: var(--bg-glass);
            border-radius: 20px;
            border: 1px solid rgba(0, 245, 255, 0.2);
            font-family: 'JetBrains Mono', monospace;
            font-size: 12px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--success-neon);
            box-shadow: 0 0 10px var(--success-neon);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* Zone principale - Layout en grille intelligente */
        .control-main {
            flex: 1;
            padding: 40px;
            display: grid;
            grid-template-columns: 300px 1fr 350px;
            grid-template-rows: auto 1fr;
            gap: 30px;
            grid-template-areas: 
                "sidebar mission-panel info-panel"
                "sidebar mission-panel info-panel";
        }

        /* Sidebar de navigation - Style futuriste */
        .control-sidebar {
            grid-area: sidebar;
            background: var(--bg-glass);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            border: 1px solid rgba(0, 245, 255, 0.2);
            padding: 30px;
            height: fit-content;
            position: sticky;
            top: 120px;
        }

        .sidebar-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 25px;
            color: var(--primary-neon);
            text-transform: uppercase;
            letter-spacing: 1px;
            font-family: 'JetBrains Mono', monospace;
        }

        .mission-steps {
            list-style: none;
        }

        .mission-step {
            margin-bottom: 15px;
            position: relative;
        }

        .step-button {
            width: 100%;
            background: transparent;
            border: 1px solid rgba(255, 255, 255, 0.1);
            color: var(--text-secondary);
            padding: 15px 20px;
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition-smooth);
            display: flex;
            align-items: center;
            gap: 15px;
            font-size: 14px;
            position: relative;
            overflow: hidden;
        }

        .step-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0, 245, 255, 0.1), transparent);
            transition: var(--transition-smooth);
        }

        .step-button:hover::before {
            left: 100%;
        }

        .step-button:hover {
            border-color: var(--primary-neon);
            color: var(--text-primary);
            box-shadow: var(--glow-primary);
            transform: translateX(5px);
        }

        .step-button.active {
            background: linear-gradient(135deg, rgba(0, 245, 255, 0.2), rgba(255, 0, 128, 0.1));
            border-color: var(--primary-neon);
            color: var(--text-primary);
            box-shadow: var(--glow-primary);
        }

        .step-icon {
            width: 20px;
            text-align: center;
        }

        .step-status {
            margin-left: auto;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--text-muted);
            transition: var(--transition-fast);
        }

        .step-button.completed .step-status {
            background: var(--success-neon);
            box-shadow: 0 0 8px var(--success-neon);
        }

        .step-button.active .step-status {
            background: var(--primary-neon);
            box-shadow: 0 0 8px var(--primary-neon);
        }

        /* Boutons d'analyse avancés avec cache */
        .analysis-actions {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .analysis-action-btn {
            width: 100%;
            background: transparent;
            border: 1px solid rgba(255, 255, 255, 0.1);
            color: var(--text-secondary);
            padding: 12px 15px;
            border-radius: 8px;
            cursor: pointer;
            transition: var(--transition-smooth);
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 13px;
            text-align: left;
            position: relative;
            overflow: hidden;
        }

        .analysis-action-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0, 245, 255, 0.1), transparent);
            transition: var(--transition-smooth);
        }

        .analysis-action-btn:hover::before {
            left: 100%;
        }

        .analysis-action-btn:hover {
            border-color: var(--primary-neon);
            color: var(--text-primary);
            background: rgba(0, 245, 255, 0.05);
            transform: translateX(3px);
            box-shadow: 0 0 15px rgba(0, 245, 255, 0.2);
        }

        .analysis-action-btn.loading {
            border-color: var(--warning-neon);
            color: var(--warning-neon);
            background: rgba(255, 170, 0, 0.05);
        }

        .analysis-action-btn.completed {
            border-color: var(--success-neon);
            color: var(--success-neon);
            background: rgba(0, 255, 136, 0.05);
        }

        .analysis-action-btn.error {
            border-color: var(--danger-neon);
            color: var(--danger-neon);
            background: rgba(255, 51, 102, 0.05);
        }

        .analysis-action-btn i {
            width: 16px;
            text-align: center;
            color: var(--primary-neon);
            transition: var(--transition-fast);
        }

        .analysis-action-btn.loading i {
            color: var(--warning-neon);
            animation: spin 1s linear infinite;
        }

        .analysis-action-btn.completed i {
            color: var(--success-neon);
        }

        .analysis-action-btn.error i {
            color: var(--danger-neon);
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Indicateurs de cache */
        .cache-indicator {
            position: absolute;
            top: 8px;
            right: 8px;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--text-muted);
            transition: var(--transition-fast);
        }

        .cache-indicator.cached {
            background: var(--success-neon);
            box-shadow: 0 0 8px var(--success-neon);
            animation: cachePulse 2s infinite;
        }

        .cache-indicator.loading {
            background: var(--warning-neon);
            animation: spin 1s linear infinite;
        }

        .cache-indicator.error {
            background: var(--danger-neon);
        }

        @keyframes cachePulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* Panel principal - Zone de mission */
        .mission-panel {
            grid-area: mission-panel;
            background: var(--bg-glass);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            border: 1px solid rgba(0, 245, 255, 0.2);
            padding: 40px;
            position: relative;
            overflow: hidden;
        }

        .mission-panel::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-neon), var(--secondary-neon), var(--purple-neon));
            animation: progressGlow 3s ease-in-out infinite;
        }

        @keyframes progressGlow {
            0%, 100% { opacity: 0.5; }
            50% { opacity: 1; }
        }

        /* Panel d'informations - Style HUD */
        .info-panel {
            grid-area: info-panel;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .info-widget {
            background: var(--bg-glass);
            backdrop-filter: blur(20px);
            border-radius: 16px;
            border: 1px solid rgba(0, 245, 255, 0.2);
            padding: 25px;
            position: relative;
        }

        .widget-title {
            font-size: 14px;
            font-weight: 600;
            color: var(--primary-neon);
            margin-bottom: 20px;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-family: 'JetBrains Mono', monospace;
        }

        /* Styles pour le contenu des étapes */
        .step-content {
            animation: slideIn 0.5s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .step-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .step-title {
            font-size: 32px;
            font-weight: 700;
            background: linear-gradient(135deg, var(--primary-neon), var(--secondary-neon));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }

        .step-subtitle {
            color: var(--text-secondary);
            font-size: 16px;
            max-width: 600px;
            margin: 0 auto;
        }

        /* Zone de chargement futuriste */
        .upload-zone {
            border: 2px dashed rgba(0, 245, 255, 0.3);
            border-radius: 20px;
            padding: 60px 40px;
            text-align: center;
            margin: 40px 0;
            background: rgba(0, 245, 255, 0.05);
            transition: var(--transition-smooth);
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .upload-zone::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: conic-gradient(from 0deg, transparent, rgba(0, 245, 255, 0.1), transparent);
            animation: rotate 4s linear infinite;
            opacity: 0;
            transition: var(--transition-smooth);
        }

        .upload-zone:hover::before {
            opacity: 1;
        }

        .upload-zone:hover {
            border-color: var(--primary-neon);
            background: rgba(0, 245, 255, 0.1);
            box-shadow: var(--glow-primary);
        }

        .upload-icon {
            font-size: 64px;
            color: var(--primary-neon);
            margin-bottom: 20px;
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .upload-text h3 {
            font-size: 24px;
            color: var(--text-primary);
            margin-bottom: 10px;
        }

        .upload-text p {
            color: var(--text-secondary);
            margin-bottom: 15px;
        }

        .upload-text small {
            color: var(--text-muted);
            font-size: 12px;
        }

        /* Grille de métriques futuriste */
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .metric-card {
            background: var(--bg-glass);
            border-radius: 16px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
            overflow: hidden;
            transition: var(--transition-smooth);
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .metric-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--primary-neon);
            transform: scaleX(0);
            transition: var(--transition-smooth);
        }

        .metric-card:hover::before {
            transform: scaleX(1);
        }

        .metric-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--glow-primary);
        }

        .metric-card.neon-blue::before { background: var(--primary-neon); }
        .metric-card.neon-green::before { background: var(--success-neon); }
        .metric-card.neon-purple::before { background: var(--purple-neon); }
        .metric-card.neon-orange::before { background: var(--warning-neon); }

        .metric-icon {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            background: rgba(0, 245, 255, 0.1);
            color: var(--primary-neon);
        }

        .metric-card.neon-green .metric-icon {
            background: rgba(0, 255, 136, 0.1);
            color: var(--success-neon);
        }

        .metric-card.neon-purple .metric-icon {
            background: rgba(139, 92, 246, 0.1);
            color: var(--purple-neon);
        }

        .metric-card.neon-orange .metric-icon {
            background: rgba(255, 170, 0, 0.1);
            color: var(--warning-neon);
        }

        .metric-data {
            flex: 1;
        }

        .metric-value {
            font-size: 28px;
            font-weight: 700;
            color: var(--text-primary);
            font-family: 'JetBrains Mono', monospace;
            margin-bottom: 5px;
        }

        .metric-label {
            color: var(--text-secondary);
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .metric-trend {
            color: var(--success-neon);
            font-size: 18px;
        }

        /* Barre de progression futuriste */
        .analysis-progress {
            margin: 40px 0;
            padding: 30px;
            background: var(--bg-glass);
            border-radius: 16px;
            border: 1px solid rgba(0, 245, 255, 0.2);
        }

        .progress-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .progress-header h3 {
            color: var(--text-primary);
            font-size: 18px;
        }

        .progress-percentage {
            color: var(--primary-neon);
            font-family: 'JetBrains Mono', monospace;
            font-weight: 600;
            font-size: 16px;
        }

        .progress-bar {
            height: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 15px;
            position: relative;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--primary-neon), var(--secondary-neon));
            border-radius: 4px;
            width: 0%;
            transition: width 0.5s ease;
            position: relative;
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .progress-status {
            color: var(--text-secondary);
            font-size: 14px;
            font-family: 'JetBrains Mono', monospace;
        }

        /* Boutons de contrôle futuristes */
        .mission-controls {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin-top: 40px;
        }

        .control-btn {
            padding: 15px 30px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-smooth);
            display: flex;
            align-items: center;
            gap: 10px;
            position: relative;
            overflow: hidden;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .control-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: var(--transition-smooth);
        }

        .control-btn:hover::before {
            left: 100%;
        }

        .control-btn.primary {
            background: linear-gradient(135deg, var(--primary-neon), var(--secondary-neon));
            color: white;
            box-shadow: var(--glow-primary);
        }

        .control-btn.primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 0 30px rgba(0, 245, 255, 0.7);
        }

        .control-btn.secondary {
            background: transparent;
            color: var(--text-primary);
            border: 2px solid rgba(0, 245, 255, 0.3);
        }

        .control-btn.secondary:hover {
            border-color: var(--primary-neon);
            box-shadow: var(--glow-primary);
            transform: translateY(-2px);
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .control-main {
                grid-template-columns: 1fr;
                grid-template-areas:
                    "mission-panel"
                    "sidebar"
                    "info-panel";
            }

            .control-sidebar {
                position: static;
            }
        }

        @media (max-width: 768px) {
            .control-header {
                padding: 15px 20px;
            }

            .control-main {
                padding: 20px;
                gap: 20px;
            }

            .logo-title {
                font-size: 24px;
            }

            .system-status {
                gap: 10px;
            }

            .metrics-grid {
                grid-template-columns: 1fr;
            }

            .mission-controls {
                flex-direction: column;
            }
        }

        /* Styles pour l'interface de classification IA */
        .ai-brain-visual {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin: 40px 0;
            padding: 40px;
            background: var(--bg-glass);
            border-radius: 20px;
            border: 1px solid rgba(139, 92, 246, 0.3);
        }

        .neural-network {
            display: flex;
            gap: 80px;
            align-items: center;
        }

        .neural-layer {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .neuron {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: rgba(139, 92, 246, 0.3);
            border: 2px solid var(--purple-neon);
            position: relative;
            transition: var(--transition-smooth);
        }

        .neuron.active {
            background: var(--purple-neon);
            box-shadow: 0 0 15px var(--purple-neon);
            animation: neuronPulse 2s infinite;
        }

        @keyframes neuronPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.2); }
        }

        .classification-result {
            text-align: center;
            flex: 1;
            margin-left: 40px;
        }

        .building-type {
            font-size: 24px;
            font-weight: 700;
            color: var(--primary-neon);
            margin: 20px 0;
            font-family: 'JetBrains Mono', monospace;
        }

        .confidence-meter {
            background: rgba(255, 255, 255, 0.1);
            height: 8px;
            border-radius: 4px;
            overflow: hidden;
            position: relative;
            margin: 20px 0;
        }

        .confidence-bar {
            height: 100%;
            background: linear-gradient(90deg, var(--danger-neon), var(--warning-neon), var(--success-neon));
            width: 0%;
            transition: width 1s ease;
        }

        .confidence-text {
            color: var(--text-primary);
            font-family: 'JetBrains Mono', monospace;
            font-weight: 600;
        }

        /* Styles pour l'interface des coûts */
        .costs-dashboard {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin: 40px 0;
        }

        .cost-summary {
            background: var(--bg-glass);
            border-radius: 20px;
            padding: 40px;
            border: 1px solid rgba(0, 245, 255, 0.3);
            text-align: center;
        }

        .cost-main {
            margin-bottom: 30px;
        }

        .cost-value {
            font-size: 48px;
            font-weight: 800;
            color: var(--primary-neon);
            font-family: 'JetBrains Mono', monospace;
            text-shadow: 0 0 20px rgba(0, 245, 255, 0.5);
        }

        .cost-label {
            color: var(--text-secondary);
            font-size: 16px;
            margin-top: 10px;
        }

        .cost-details {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .cost-item {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .cost-breakdown {
            background: var(--bg-glass);
            border-radius: 20px;
            padding: 40px;
            border: 1px solid rgba(255, 0, 128, 0.3);
        }

        /* Styles pour l'interface environnementale */
        .environment-dashboard {
            display: flex;
            gap: 40px;
            margin: 40px 0;
            align-items: center;
        }

        .sustainability-score {
            text-align: center;
            background: var(--bg-glass);
            border-radius: 20px;
            padding: 40px;
            border: 1px solid rgba(0, 255, 136, 0.3);
        }

        .score-circle {
            position: relative;
            margin-bottom: 20px;
        }

        .score-ring {
            transform: rotate(-90deg);
        }

        .score-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            display: flex;
            align-items: baseline;
            gap: 5px;
        }

        .score-value {
            font-size: 32px;
            font-weight: 800;
            color: var(--success-neon);
            font-family: 'JetBrains Mono', monospace;
        }

        .score-max {
            font-size: 16px;
            color: var(--text-secondary);
        }

        .environmental-metrics {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .env-metric {
            display: flex;
            align-items: center;
            gap: 20px;
            background: var(--bg-glass);
            padding: 25px;
            border-radius: 16px;
            border: 1px solid rgba(0, 255, 136, 0.2);
        }

        .env-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            background: rgba(0, 255, 136, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--success-neon);
            font-size: 20px;
        }

        .env-value {
            font-size: 24px;
            font-weight: 700;
            color: var(--text-primary);
            font-family: 'JetBrains Mono', monospace;
        }

        .env-label {
            color: var(--text-secondary);
            font-size: 14px;
        }

        /* Styles pour l'interface d'optimisation */
        .optimization-lab {
            margin: 40px 0;
        }

        .genetic-algorithm {
            background: var(--bg-glass);
            border-radius: 20px;
            padding: 40px;
            border: 1px solid rgba(255, 170, 0, 0.3);
            margin-bottom: 30px;
        }

        .generations-display {
            display: flex;
            align-items: center;
            gap: 40px;
            margin-top: 20px;
        }

        .generation {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .generation-number, .fitness-score {
            font-family: 'JetBrains Mono', monospace;
            color: var(--text-primary);
        }

        .generation-number span, .fitness-score span {
            color: var(--warning-neon);
            font-weight: 700;
        }

        .evolution-progress {
            flex: 1;
            height: 12px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 6px;
            overflow: hidden;
        }

        .evolution-bar {
            height: 100%;
            background: linear-gradient(90deg, var(--warning-neon), var(--success-neon));
            width: 0%;
            transition: width 0.5s ease;
        }

        .pareto-solutions {
            background: var(--bg-glass);
            border-radius: 20px;
            padding: 40px;
            border: 1px solid rgba(255, 0, 128, 0.3);
        }

        .solutions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        /* Styles pour l'interface de rapport */
        .report-generator {
            display: flex;
            gap: 40px;
            margin: 40px 0;
            align-items: flex-start;
        }

        .report-preview {
            background: var(--bg-glass);
            border-radius: 20px;
            padding: 40px;
            border: 1px solid rgba(255, 0, 128, 0.3);
            text-align: center;
            flex: 1;
        }

        .document-icon {
            font-size: 80px;
            color: var(--secondary-neon);
            margin-bottom: 20px;
            animation: documentFloat 3s ease-in-out infinite;
        }

        @keyframes documentFloat {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .report-sections {
            flex: 1;
            background: var(--bg-glass);
            border-radius: 20px;
            padding: 40px;
            border: 1px solid rgba(0, 245, 255, 0.3);
        }

        .section-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            color: var(--text-secondary);
        }

        .section-item.completed {
            color: var(--success-neon);
        }

        .section-item.completed i {
            color: var(--success-neon);
        }

        /* Scanner d'anomalies - Style radar */
        .anomalies-scanner {
            margin: 40px 0;
        }

        .scanner-visual {
            display: flex;
            align-items: center;
            gap: 40px;
            background: var(--bg-glass);
            border-radius: 20px;
            padding: 40px;
            border: 1px solid rgba(255, 51, 102, 0.3);
            margin-bottom: 30px;
        }

        .radar-container {
            width: 200px;
            height: 200px;
            border-radius: 50%;
            border: 2px solid var(--danger-neon);
            position: relative;
            background: radial-gradient(circle, rgba(255, 51, 102, 0.1) 0%, transparent 70%);
        }

        .radar-sweep {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 2px;
            height: 90px;
            background: linear-gradient(to top, transparent, var(--danger-neon));
            transform-origin: bottom center;
            transform: translate(-50%, -100%) rotate(0deg);
            animation: radarSweep 3s linear infinite;
        }

        @keyframes radarSweep {
            0% { transform: translate(-50%, -100%) rotate(0deg); }
            100% { transform: translate(-50%, -100%) rotate(360deg); }
        }

        .radar-grid {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 100%;
            height: 100%;
            border-radius: 50%;
            border: 1px solid rgba(255, 51, 102, 0.3);
        }

        .radar-grid::before,
        .radar-grid::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            border-radius: 50%;
            border: 1px solid rgba(255, 51, 102, 0.2);
        }

        .radar-grid::before {
            width: 66%;
            height: 66%;
        }

        .radar-grid::after {
            width: 33%;
            height: 33%;
        }

        .radar-center {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--danger-neon);
            box-shadow: 0 0 15px var(--danger-neon);
        }

        .scanner-status {
            flex: 1;
            text-align: center;
        }

        .scanner-status h3 {
            color: var(--danger-neon);
            font-size: 24px;
            margin-bottom: 10px;
        }

        .severity-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
        }

        .severity-card {
            background: var(--bg-glass);
            border-radius: 16px;
            padding: 25px;
            text-align: center;
            border: 2px solid;
            transition: var(--transition-smooth);
        }

        .severity-card.critical {
            border-color: var(--danger-neon);
        }

        .severity-card.high {
            border-color: var(--warning-neon);
        }

        .severity-card.medium {
            border-color: var(--primary-neon);
        }

        .severity-card.low {
            border-color: var(--success-neon);
        }

        .severity-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 0 20px rgba(255, 255, 255, 0.1);
        }

        .severity-icon {
            font-size: 32px;
            margin-bottom: 15px;
        }

        .severity-card.critical .severity-icon {
            color: var(--danger-neon);
        }

        .severity-card.high .severity-icon {
            color: var(--warning-neon);
        }

        .severity-card.medium .severity-icon {
            color: var(--primary-neon);
        }

        .severity-card.low .severity-icon {
            color: var(--success-neon);
        }

        .severity-count {
            font-size: 36px;
            font-weight: 800;
            font-family: 'JetBrains Mono', monospace;
            color: var(--text-primary);
            margin-bottom: 10px;
        }

        .severity-label {
            color: var(--text-secondary);
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        /* Styles pour les métriques temps réel */
        .metric-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .metric-item:last-child {
            border-bottom: none;
        }

        .metric-name {
            color: var(--text-secondary);
            font-size: 14px;
            font-family: 'JetBrains Mono', monospace;
        }

        .metric-value {
            color: var(--primary-neon);
            font-weight: 600;
            font-family: 'JetBrains Mono', monospace;
        }

        /* Animations supplémentaires */
        .mission-panel {
            transition: var(--transition-smooth);
        }

        /* Effets de survol pour les widgets */
        .info-widget:hover {
            transform: translateY(-2px);
            box-shadow: var(--glow-primary);
        }

        /* Styles pour le sélecteur de projet principal */
        .project-selector-main {
            margin: 40px 0;
            text-align: center;
            background: var(--bg-glass);
            padding: 30px;
            border-radius: 20px;
            border: 1px solid rgba(0, 245, 255, 0.2);
        }

        .project-selector-main h3 {
            color: var(--text-primary);
            margin-bottom: 25px;
            font-size: 20px;
            background: linear-gradient(135deg, var(--primary-neon), var(--secondary-neon));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .project-dropdown {
            position: relative;
            max-width: 400px;
            margin: 0 auto;
        }

        #project-select {
            width: 100%;
            padding: 15px 20px;
            background: var(--bg-card);
            border: 2px solid rgba(0, 245, 255, 0.3);
            border-radius: 12px;
            color: var(--text-primary);
            font-size: 16px;
            font-family: 'Inter', sans-serif;
            cursor: pointer;
            transition: var(--transition-smooth);
        }

        #project-select:focus {
            outline: none;
            border-color: var(--primary-neon);
            box-shadow: var(--glow-primary);
        }

        #project-select option {
            background: var(--bg-card);
            color: var(--text-primary);
            padding: 10px;
        }

        /* Styles pour les projets existants */
        .project-selector {
            margin: 40px 0;
            text-align: center;
        }

        .project-selector h3 {
            color: var(--text-primary);
            margin-bottom: 20px;
            font-size: 18px;
        }

        .projects-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .project-card {
            background: var(--bg-glass);
            border-radius: 12px;
            padding: 20px;
            border: 1px solid rgba(0, 245, 255, 0.2);
            cursor: pointer;
            transition: var(--transition-smooth);
            text-align: center;
        }

        .project-card:hover {
            border-color: var(--primary-neon);
            box-shadow: var(--glow-primary);
            transform: translateY(-3px);
        }

        .project-icon {
            font-size: 32px;
            color: var(--primary-neon);
            margin-bottom: 15px;
        }

        .project-name {
            color: var(--text-primary);
            font-weight: 600;
            margin-bottom: 5px;
        }

        .project-info {
            color: var(--text-secondary);
            font-size: 12px;
        }

        /* Styles pour l'assistant IA */
        .ai-status {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 15px;
            background: rgba(0, 245, 255, 0.1);
            border-radius: 12px;
            border: 1px solid rgba(0, 245, 255, 0.3);
        }

        .ai-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-neon), var(--secondary-neon));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
        }

        .ai-message {
            flex: 1;
        }

        .ai-name {
            color: var(--primary-neon);
            font-weight: 600;
            font-size: 14px;
            margin-bottom: 5px;
        }

        .ai-text {
            color: var(--text-secondary);
            font-size: 12px;
            line-height: 1.4;
        }

        /* Responsive amélioré */
        @media (max-width: 1400px) {
            .costs-dashboard,
            .environment-dashboard,
            .report-generator {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .ai-brain-visual,
            .scanner-visual {
                flex-direction: column;
                text-align: center;
            }

            .neural-network {
                gap: 40px;
            }

            .classification-result {
                margin-left: 0;
                margin-top: 20px;
            }

            .severity-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 480px) {
            .severity-grid {
                grid-template-columns: 1fr;
            }

            .step-title {
                font-size: 24px;
            }

            .cost-value {
                font-size: 36px;
            }
        }

        /* Styles pour les pop-ups d'analyse */
        .analysis-popup {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 10000;
            opacity: 0;
            visibility: hidden;
            transition: var(--transition-smooth);
        }

        .analysis-popup.active {
            opacity: 1;
            visibility: visible;
        }

        .popup-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
        }

        .popup-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: var(--bg-card);
            border-radius: 20px;
            border: 1px solid rgba(0, 245, 255, 0.3);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
            max-width: 800px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .popup-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 25px 30px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            background: linear-gradient(135deg, rgba(0, 245, 255, 0.1), rgba(255, 0, 128, 0.1));
        }

        .popup-header h3 {
            color: var(--text-primary);
            font-size: 20px;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .popup-close {
            background: none;
            border: none;
            color: var(--text-secondary);
            font-size: 20px;
            cursor: pointer;
            padding: 5px;
            border-radius: 50%;
            transition: var(--transition-fast);
        }

        .popup-close:hover {
            color: var(--text-primary);
            background: rgba(255, 255, 255, 0.1);
        }

        .popup-body {
            padding: 30px;
        }

        .analysis-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .analysis-card {
            background: var(--bg-glass);
            padding: 20px;
            border-radius: 12px;
            border: 1px solid rgba(0, 245, 255, 0.2);
            text-align: center;
        }

        .analysis-card h4 {
            color: var(--text-secondary);
            font-size: 14px;
            margin-bottom: 15px;
        }

        .metric-large {
            font-size: 32px;
            font-weight: 700;
            color: var(--primary-neon);
            font-family: 'JetBrains Mono', monospace;
        }

        .pmr-analysis {
            text-align: center;
        }

        .compliance-score {
            margin-bottom: 30px;
        }

        .score-circle {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            border: 4px solid var(--primary-neon);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 20px auto;
            background: rgba(0, 245, 255, 0.1);
        }

        .score-value {
            font-size: 24px;
            font-weight: 700;
            color: var(--primary-neon);
        }

        .classification-result {
            text-align: center;
        }

        .building-type-result {
            margin: 20px 0;
            padding: 20px;
            background: var(--bg-glass);
            border-radius: 12px;
        }

        .type-label {
            color: var(--text-secondary);
            margin-right: 10px;
        }

        .type-value {
            color: var(--primary-neon);
            font-weight: 600;
            font-size: 18px;
        }

        .confidence-bar {
            margin-top: 20px;
        }

        .confidence-bar .bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            overflow: hidden;
            margin-top: 10px;
        }

        .confidence-bar .fill {
            height: 100%;
            background: linear-gradient(90deg, var(--primary-neon), var(--secondary-neon));
            transition: width 1s ease;
        }

        /* Styles pour les pop-ups de chargement */
        .loading-popup .popup-content {
            text-align: center;
        }

        .loading-container {
            padding: 40px 20px;
        }

        .loading-spinner {
            position: relative;
            width: 80px;
            height: 80px;
            margin: 0 auto 30px;
        }

        .spinner-ring {
            position: absolute;
            width: 100%;
            height: 100%;
            border: 3px solid transparent;
            border-top: 3px solid var(--primary-neon);
            border-radius: 50%;
            animation: spinRing 1.5s linear infinite;
        }

        .spinner-ring:nth-child(2) {
            width: 60px;
            height: 60px;
            top: 10px;
            left: 10px;
            border-top-color: var(--secondary-neon);
            animation-duration: 1s;
            animation-direction: reverse;
        }

        .spinner-ring:nth-child(3) {
            width: 40px;
            height: 40px;
            top: 20px;
            left: 20px;
            border-top-color: var(--success-neon);
            animation-duration: 0.8s;
        }

        @keyframes spinRing {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-container h4 {
            color: var(--text-primary);
            font-size: 20px;
            margin-bottom: 15px;
        }

        .loading-container p {
            color: var(--text-secondary);
            font-size: 14px;
        }

        /* Styles pour l'analyse complète */
        .complete-analysis-content {
            max-height: 70vh;
            overflow-y: auto;
        }

        .analysis-summary {
            margin-bottom: 30px;
        }

        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .summary-card {
            background: var(--bg-glass);
            border-radius: 12px;
            padding: 20px;
            border: 1px solid rgba(0, 245, 255, 0.2);
            display: flex;
            align-items: center;
            gap: 15px;
            transition: var(--transition-smooth);
        }

        .summary-card:hover {
            border-color: var(--primary-neon);
            box-shadow: 0 0 15px rgba(0, 245, 255, 0.2);
            transform: translateY(-2px);
        }

        .summary-icon {
            width: 50px;
            height: 50px;
            border-radius: 10px;
            background: linear-gradient(135deg, var(--primary-neon), var(--secondary-neon));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
        }

        .summary-value {
            font-size: 24px;
            font-weight: 700;
            color: var(--text-primary);
            font-family: 'JetBrains Mono', monospace;
        }

        .summary-label {
            color: var(--text-secondary);
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .analysis-details {
            margin-bottom: 30px;
        }

        .details-table {
            background: var(--bg-glass);
            border-radius: 12px;
            overflow: hidden;
            border: 1px solid rgba(0, 245, 255, 0.2);
        }

        .details-table-content {
            width: 100%;
            border-collapse: collapse;
        }

        .details-table-content th {
            background: rgba(0, 245, 255, 0.1);
            color: var(--primary-neon);
            padding: 15px;
            text-align: left;
            font-weight: 600;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .details-table-content td {
            padding: 12px 15px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            color: var(--text-primary);
        }

        .details-table-content tr:hover {
            background: rgba(0, 245, 255, 0.05);
        }

        .analysis-actions-footer {
            display: flex;
            gap: 15px;
            justify-content: center;
            padding-top: 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .action-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-smooth);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .action-btn.primary {
            background: linear-gradient(135deg, var(--primary-neon), var(--secondary-neon));
            color: white;
        }

        .action-btn.secondary {
            background: transparent;
            color: var(--text-primary);
            border: 2px solid rgba(0, 245, 255, 0.3);
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 0 15px rgba(0, 245, 255, 0.3);
        }

        /* Styles pour la classification IA */
        .classification-content {
            max-height: 70vh;
            overflow-y: auto;
        }

        .ai-brain-visual {
            display: flex;
            gap: 40px;
            align-items: center;
            margin-bottom: 30px;
            padding: 30px;
            background: var(--bg-glass);
            border-radius: 16px;
            border: 1px solid rgba(139, 92, 246, 0.3);
        }

        .neural-network-display {
            display: flex;
            align-items: center;
            gap: 30px;
        }

        .network-layer {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .neuron {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: var(--purple-neon);
            box-shadow: 0 0 10px var(--purple-neon);
            animation: neuronPulse 2s infinite;
        }

        @keyframes neuronPulse {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.7; transform: scale(1.1); }
        }

        .network-connections {
            width: 20px;
            height: 2px;
            background: linear-gradient(90deg, var(--purple-neon), transparent);
            position: relative;
        }

        .building-type-display {
            display: flex;
            align-items: center;
            gap: 20px;
            margin: 20px 0;
        }

        .type-icon {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            background: linear-gradient(135deg, var(--primary-neon), var(--secondary-neon));
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
        }

        .type-name {
            font-size: 24px;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 5px;
        }

        .type-description {
            color: var(--text-secondary);
            font-size: 14px;
        }

        .confidence-display {
            margin-top: 20px;
        }

        .confidence-label {
            color: var(--text-secondary);
            font-size: 14px;
            margin-bottom: 10px;
        }

        .confidence-bar-container {
            position: relative;
            height: 12px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 6px;
            overflow: hidden;
        }

        .confidence-bar-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--danger-neon), var(--warning-neon), var(--success-neon));
            border-radius: 6px;
            transition: width 1s ease;
        }

        .confidence-percentage {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-primary);
            font-weight: 600;
            font-size: 12px;
        }

        .classification-details {
            margin-bottom: 30px;
        }

        .details-grid {
            display: grid;
            gap: 15px;
        }

        .detail-item {
            display: flex;
            justify-content: space-between;
            padding: 12px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .detail-label {
            color: var(--text-secondary);
        }

        .detail-value {
            color: var(--text-primary);
            font-weight: 600;
        }

        /* Styles pour l'analyse PMR */
        .pmr-analysis-content {
            max-height: 70vh;
            overflow-y: auto;
        }

        .pmr-score-display {
            text-align: center;
            margin-bottom: 30px;
            padding: 30px;
            background: var(--bg-glass);
            border-radius: 16px;
            border: 1px solid rgba(0, 255, 136, 0.3);
        }

        .score-circle-container {
            position: relative;
            display: inline-block;
            margin-bottom: 20px;
        }

        .score-circle {
            transform: rotate(-90deg);
        }

        .score-progress-circle {
            transition: stroke-dashoffset 2s ease;
        }

        .score-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
        }

        .score-value {
            font-size: 32px;
            font-weight: 800;
            color: var(--success-neon);
            font-family: 'JetBrains Mono', monospace;
        }

        .score-label {
            font-size: 12px;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 10px 20px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 14px;
        }

        .status-badge.success {
            background: rgba(0, 255, 136, 0.2);
            color: var(--success-neon);
            border: 1px solid var(--success-neon);
        }

        .status-badge.warning {
            background: rgba(255, 170, 0, 0.2);
            color: var(--warning-neon);
            border: 1px solid var(--warning-neon);
        }

        .status-badge.danger {
            background: rgba(255, 51, 102, 0.2);
            color: var(--danger-neon);
            border: 1px solid var(--danger-neon);
        }

        .pmr-details, .pmr-recommendations {
            margin-bottom: 30px;
        }

        .compliance-items, .recommendations-list {
            display: grid;
            gap: 10px;
            margin-top: 15px;
        }

        .compliance-item, .recommendation-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            background: var(--bg-glass);
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .compliance-item.compliant {
            border-color: var(--success-neon);
            background: rgba(0, 255, 136, 0.05);
        }

        .compliance-item.non-compliant {
            border-color: var(--danger-neon);
            background: rgba(255, 51, 102, 0.05);
        }

        /* Styles pour l'assistant IA */
        .ai-assistant-content {
            max-height: 70vh;
            overflow-y: auto;
        }

        .ai-avatar-section {
            display: flex;
            align-items: center;
            gap: 20px;
            margin-bottom: 30px;
            padding: 25px;
            background: var(--bg-glass);
            border-radius: 16px;
            border: 1px solid rgba(0, 245, 255, 0.3);
        }

        .ai-avatar {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-neon), var(--secondary-neon));
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 28px;
            color: white;
            animation: aiPulse 3s ease-in-out infinite;
        }

        @keyframes aiPulse {
            0%, 100% { box-shadow: 0 0 20px rgba(0, 245, 255, 0.5); }
            50% { box-shadow: 0 0 30px rgba(255, 0, 128, 0.7); }
        }

        .ai-intro h4 {
            color: var(--text-primary);
            font-size: 20px;
            margin-bottom: 5px;
        }

        .ai-intro p {
            color: var(--text-secondary);
            font-size: 14px;
        }

        .ai-recommendations, .ai-insights {
            margin-bottom: 30px;
        }

        .recommendations-container, .insights-container {
            display: grid;
            gap: 15px;
            margin-top: 15px;
        }

        .ai-recommendation {
            display: flex;
            align-items: flex-start;
            gap: 15px;
            padding: 20px;
            background: var(--bg-glass);
            border-radius: 12px;
            border: 1px solid rgba(0, 245, 255, 0.2);
            animation: slideInLeft 0.5s ease-out;
        }

        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .rec-icon {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: var(--primary-neon);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            flex-shrink: 0;
        }

        .rec-content {
            flex: 1;
        }

        .rec-text {
            color: var(--text-primary);
            font-weight: 500;
            margin-bottom: 5px;
        }

        .rec-priority {
            color: var(--text-secondary);
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .ai-insight {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px;
            background: var(--bg-glass);
            border-radius: 8px;
            border: 1px solid rgba(139, 92, 246, 0.2);
        }

        .ai-insight i {
            color: var(--purple-neon);
            font-size: 16px;
        }

        .ai-insight span {
            color: var(--text-primary);
            font-style: italic;
        }

        /* Styles pour la prédiction des coûts */
        .cost-prediction-content {
            max-height: 70vh;
            overflow-y: auto;
        }

        .cost-summary-display {
            text-align: center;
            margin-bottom: 30px;
            padding: 30px;
            background: var(--bg-glass);
            border-radius: 16px;
            border: 1px solid rgba(255, 215, 0, 0.3);
        }

        .cost-main-value {
            margin-bottom: 25px;
        }

        .cost-amount {
            font-size: 48px;
            font-weight: 800;
            color: var(--warning-neon);
            font-family: 'JetBrains Mono', monospace;
            text-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
        }

        .cost-label {
            color: var(--text-secondary);
            font-size: 16px;
            text-transform: uppercase;
            letter-spacing: 2px;
        }

        .cost-details-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
        }

        .cost-detail {
            padding: 20px;
            background: rgba(255, 215, 0, 0.1);
            border-radius: 12px;
            border: 1px solid rgba(255, 215, 0, 0.2);
        }

        .detail-value {
            font-size: 24px;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 5px;
        }

        .detail-label {
            color: var(--text-secondary);
            font-size: 12px;
            text-transform: uppercase;
        }

        .cost-breakdown, .cost-comparison {
            margin-bottom: 30px;
        }

        .breakdown-items {
            display: grid;
            gap: 12px;
            margin-top: 15px;
        }

        .breakdown-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px;
            background: var(--bg-glass);
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .breakdown-color {
            width: 20px;
            height: 20px;
            border-radius: 4px;
        }

        .breakdown-info {
            flex: 1;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .breakdown-category {
            color: var(--text-primary);
            font-weight: 500;
        }

        .breakdown-percentage {
            color: var(--text-secondary);
            font-weight: 600;
        }

        .comparison-indicator {
            margin-top: 15px;
        }

        .comparison-bar {
            position: relative;
            height: 20px;
            background: linear-gradient(90deg, var(--success-neon), var(--warning-neon), var(--danger-neon));
            border-radius: 10px;
            margin-bottom: 10px;
        }

        .comparison-marker {
            position: absolute;
            top: -30px;
            transform: translateX(-50%);
            background: var(--text-primary);
            color: var(--bg-dark);
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
        }

        .comparison-marker::after {
            content: '';
            position: absolute;
            top: 100%;
            left: 50%;
            transform: translateX(-50%);
            border: 5px solid transparent;
            border-top-color: var(--text-primary);
        }

        .comparison-labels {
            display: flex;
            justify-content: space-between;
            color: var(--text-secondary);
            font-size: 12px;
        }

        /* Styles pour l'analyse environnementale */
        .environmental-analysis-content {
            max-height: 70vh;
            overflow-y: auto;
        }

        .sustainability-overview {
            display: flex;
            gap: 30px;
            align-items: center;
            margin-bottom: 30px;
            padding: 30px;
            background: var(--bg-glass);
            border-radius: 16px;
            border: 1px solid rgba(0, 255, 136, 0.3);
        }

        .sustainability-score-display {
            text-align: center;
        }

        .score-ring {
            position: relative;
            margin-bottom: 15px;
        }

        .score-ring svg {
            transform: rotate(-90deg);
        }

        .score-progress {
            transition: stroke-dashoffset 2s ease;
        }

        .score-center {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
        }

        .score-number {
            font-size: 32px;
            font-weight: 800;
            color: var(--success-neon);
        }

        .score-max {
            font-size: 16px;
            color: var(--text-secondary);
        }

        .environmental-metrics {
            display: grid;
            gap: 20px;
            flex: 1;
        }

        .env-metric {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px;
            background: rgba(0, 255, 136, 0.05);
            border-radius: 8px;
            border: 1px solid rgba(0, 255, 136, 0.2);
        }

        .env-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            background: var(--success-neon);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
        }

        .env-value {
            font-size: 20px;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 2px;
        }

        .env-label {
            color: var(--text-secondary);
            font-size: 12px;
            text-transform: uppercase;
        }

        .eco-recommendations {
            display: grid;
            gap: 10px;
            margin-top: 15px;
        }

        .eco-recommendation {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            background: var(--bg-glass);
            border-radius: 8px;
            border: 1px solid rgba(0, 255, 136, 0.2);
        }

        .eco-recommendation i {
            color: var(--success-neon);
        }

        /* Styles pour l'optimisation */
        .optimization-content {
            max-height: 70vh;
            overflow-y: auto;
        }

        .optimization-overview {
            display: flex;
            gap: 40px;
            align-items: center;
            margin-bottom: 30px;
            padding: 30px;
            background: var(--bg-glass);
            border-radius: 16px;
            border: 1px solid rgba(255, 0, 128, 0.3);
        }

        .optimization-score {
            text-align: center;
        }

        .score-gauge {
            position: relative;
            width: 200px;
            height: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 15px;
        }

        .gauge-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--danger-neon), var(--warning-neon), var(--success-neon));
            border-radius: 10px;
            transition: width 2s ease;
        }

        .gauge-value {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: var(--text-primary);
            font-weight: 700;
            font-size: 14px;
        }

        .savings-potential {
            text-align: center;
        }

        .savings-amount {
            font-size: 36px;
            font-weight: 800;
            color: var(--secondary-neon);
            font-family: 'JetBrains Mono', monospace;
            margin-bottom: 5px;
        }

        .savings-label {
            color: var(--text-secondary);
            font-size: 14px;
            text-transform: uppercase;
        }

        .improvements-list {
            display: grid;
            gap: 15px;
            margin-top: 15px;
        }

        .improvement-item {
            padding: 20px;
            background: var(--bg-glass);
            border-radius: 12px;
            border: 1px solid rgba(255, 0, 128, 0.2);
        }

        .improvement-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .improvement-category {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
        }

        .improvement-potential {
            font-size: 20px;
            font-weight: 700;
            color: var(--secondary-neon);
        }

        .improvement-priority {
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 1px;
            padding: 4px 8px;
            border-radius: 4px;
        }

        .priority-haute {
            background: rgba(255, 51, 102, 0.2);
            color: var(--danger-neon);
        }

        .priority-moyenne {
            background: rgba(255, 170, 0, 0.2);
            color: var(--warning-neon);
        }

        .priority-faible {
            background: rgba(0, 255, 136, 0.2);
            color: var(--success-neon);
        }

        /* Styles pour la détection d'anomalies */
        .anomaly-detection-content {
            max-height: 70vh;
            overflow-y: auto;
        }

        .anomalies-summary {
            display: flex;
            gap: 30px;
            align-items: center;
            margin-bottom: 30px;
            padding: 30px;
            background: var(--bg-glass);
            border-radius: 16px;
            border: 1px solid rgba(255, 51, 102, 0.3);
        }

        .total-anomalies {
            text-align: center;
        }

        .anomaly-count {
            font-size: 48px;
            font-weight: 800;
            color: var(--danger-neon);
            font-family: 'JetBrains Mono', monospace;
        }

        .anomaly-label {
            color: var(--text-secondary);
            font-size: 14px;
            text-transform: uppercase;
        }

        .severity-breakdown {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            flex: 1;
        }

        .severity-item {
            text-align: center;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid;
        }

        .severity-item.critical {
            border-color: var(--danger-neon);
            background: rgba(255, 51, 102, 0.1);
        }

        .severity-item.high {
            border-color: var(--warning-neon);
            background: rgba(255, 170, 0, 0.1);
        }

        .severity-item.medium {
            border-color: var(--primary-neon);
            background: rgba(0, 245, 255, 0.1);
        }

        .severity-item.low {
            border-color: var(--success-neon);
            background: rgba(0, 255, 136, 0.1);
        }

        .severity-count {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .severity-item.critical .severity-count {
            color: var(--danger-neon);
        }

        .severity-item.high .severity-count {
            color: var(--warning-neon);
        }

        .severity-item.medium .severity-count {
            color: var(--primary-neon);
        }

        .severity-item.low .severity-count {
            color: var(--success-neon);
        }

        .severity-label {
            color: var(--text-secondary);
            font-size: 12px;
            text-transform: uppercase;
        }

        .anomalies-container {
            display: grid;
            gap: 12px;
            margin-top: 15px;
        }

        .anomaly-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px;
            background: var(--bg-glass);
            border-radius: 8px;
            border: 1px solid;
        }

        .anomaly-item.severity-critical {
            border-color: var(--danger-neon);
            background: rgba(255, 51, 102, 0.05);
        }

        .anomaly-item.severity-high {
            border-color: var(--warning-neon);
            background: rgba(255, 170, 0, 0.05);
        }

        .anomaly-item.severity-medium {
            border-color: var(--primary-neon);
            background: rgba(0, 245, 255, 0.05);
        }

        .anomaly-item.severity-low {
            border-color: var(--success-neon);
            background: rgba(0, 255, 136, 0.05);
        }

        .anomaly-icon {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
        }

        .anomaly-item.severity-critical .anomaly-icon {
            background: var(--danger-neon);
            color: white;
        }

        .anomaly-item.severity-high .anomaly-icon {
            background: var(--warning-neon);
            color: white;
        }

        .anomaly-item.severity-medium .anomaly-icon {
            background: var(--primary-neon);
            color: white;
        }

        .anomaly-item.severity-low .anomaly-icon {
            background: var(--success-neon);
            color: white;
        }

        .anomaly-info {
            flex: 1;
        }

        .anomaly-type {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 3px;
        }

        .anomaly-description {
            color: var(--text-secondary);
            font-size: 14px;
        }

        .anomaly-severity {
            font-size: 12px;
            text-transform: uppercase;
            font-weight: 600;
            padding: 4px 8px;
            border-radius: 4px;
        }

        .anomaly-item.severity-critical .anomaly-severity {
            background: rgba(255, 51, 102, 0.2);
            color: var(--danger-neon);
        }

        .anomaly-item.severity-high .anomaly-severity {
            background: rgba(255, 170, 0, 0.2);
            color: var(--warning-neon);
        }

        .anomaly-item.severity-medium .anomaly-severity {
            background: rgba(0, 245, 255, 0.2);
            color: var(--primary-neon);
        }

        .anomaly-item.severity-low .anomaly-severity {
            background: rgba(0, 255, 136, 0.2);
            color: var(--success-neon);
        }

        /* 🎨 STYLES POUR LES POP-UPS MODERNES (copiés de bim_analysis.html) */
        .modern-popup {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            opacity: 0;
            transform: scale(0.9);
            transition: all 0.3s ease;
        }

        .modern-popup.show {
            opacity: 1;
            transform: scale(1);
        }

        .modern-popup.hide {
            opacity: 0;
            transform: scale(0.9);
        }

        .modern-popup .popup-content {
            background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
            border-radius: 20px;
            max-width: 900px;
            width: 90%;
            max-height: 90vh;
            overflow: hidden;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .modern-popup .popup-header {
            padding: 25px 30px;
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .modern-popup .popup-header h2 {
            margin: 0;
            font-size: 24px;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .modern-popup .popup-close {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .modern-popup .popup-close:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: rotate(90deg);
        }

        .modern-popup .popup-body {
            padding: 30px;
            max-height: calc(90vh - 120px);
            overflow-y: auto;
            color: #1f2937;
        }

        /* Styles pour les onglets */
        .tabs {
            display: flex;
            border-bottom: 2px solid #e5e7eb;
            margin-bottom: 30px;
        }

        .tab-button {
            background: none;
            border: none;
            padding: 15px 25px;
            cursor: pointer;
            font-weight: 600;
            color: #6b7280;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
            font-size: 16px;
        }

        .tab-button.active {
            color: #3b82f6;
            border-bottom-color: #3b82f6;
        }

        .tab-button:hover {
            color: #3b82f6;
            background: rgba(59, 130, 246, 0.05);
        }

        .tab-pane {
            display: none;
        }

        .tab-pane.active {
            display: block;
        }

        /* Styles pour les résultats */
        .results-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .result-card {
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .result-card:hover {
            transform: translateY(-5px);
        }

        .result-card h4 {
            margin: 0 0 15px 0;
            font-size: 16px;
            opacity: 0.9;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .metric-value {
            font-size: 32px;
            font-weight: 800;
            margin-bottom: 8px;
            font-family: 'JetBrains Mono', monospace;
        }

        .metric-subtitle {
            font-size: 14px;
            opacity: 0.8;
        }

        /* Styles pour la répartition des éléments */
        .element-breakdown {
            background: #f8fafc;
            border-radius: 15px;
            padding: 25px;
        }

        .breakdown-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }

        .breakdown-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .breakdown-item:hover {
            transform: translateX(5px);
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        }

        .breakdown-icon {
            width: 45px;
            height: 45px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
        }

        .breakdown-info {
            flex: 1;
        }

        .breakdown-type {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 3px;
        }

        .breakdown-count {
            color: #6b7280;
            font-size: 14px;
        }

        /* Styles pour les informations de projet */
        .project-info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
        }

        .info-card {
            background: linear-gradient(145deg, #f8fafc 0%, #e2e8f0 100%);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }

        .info-card h4 {
            margin: 0 0 20px 0;
            color: #1f2937;
            font-size: 18px;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .info-item:last-child {
            border-bottom: none;
        }

        .info-item span:first-child {
            color: #6b7280;
            font-weight: 500;
        }

        .info-item span:last-child {
            color: #1f2937;
            font-weight: 600;
        }

        /* Styles pour les détails techniques */
        .details-section {
            background: #f8fafc;
            border-radius: 15px;
            padding: 25px;
        }

        .technical-details {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 25px;
        }

        .detail-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #e5e7eb;
        }

        .detail-item:last-child {
            border-bottom: none;
        }

        .element-details-table {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .element-details-table table {
            width: 100%;
            border-collapse: collapse;
        }

        .element-details-table th {
            background: #3b82f6;
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: 600;
        }

        .element-details-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #e5e7eb;
        }

        .element-details-table tr:hover {
            background: #f8fafc;
        }

        /* Styles pour la classification */
        .classification-result {
            text-align: center;
        }

        .building-type-card {
            background: linear-gradient(145deg, #f8fafc 0%, #e2e8f0 100%);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            display: flex;
            align-items: center;
            gap: 30px;
        }

        .type-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #8b5cf6, #7c3aed);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 32px;
        }

        .type-info {
            flex: 1;
            text-align: left;
        }

        .type-info h3 {
            margin: 0 0 15px 0;
            font-size: 28px;
            color: #1f2937;
        }

        .confidence-bar {
            position: relative;
            height: 12px;
            background: #e5e7eb;
            border-radius: 6px;
            overflow: hidden;
        }

        .confidence-fill {
            height: 100%;
            background: linear-gradient(90deg, #ef4444, #f59e0b, #10b981);
            border-radius: 6px;
            transition: width 1s ease;
        }

        .confidence-text {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 12px;
            font-weight: 600;
            color: #1f2937;
        }

        .features-analysis, .type-indicators {
            margin-bottom: 30px;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .feature-item {
            background: #f8fafc;
            padding: 15px;
            border-radius: 10px;
            display: flex;
            justify-content: space-between;
        }

        .feature-name {
            font-weight: 600;
            color: #374151;
        }

        .feature-value {
            color: #6b7280;
        }

        .indicators-list {
            display: grid;
            gap: 15px;
            margin-top: 15px;
        }

        .indicator-item {
            background: #f8fafc;
            padding: 20px;
            border-radius: 10px;
        }

        .indicator-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .indicator-type {
            font-weight: 600;
            color: #374151;
        }

        .indicator-score {
            font-weight: 700;
            color: #8b5cf6;
        }

        .indicator-bar {
            height: 8px;
            background: #e5e7eb;
            border-radius: 4px;
            overflow: hidden;
        }

        .indicator-fill {
            height: 100%;
            background: linear-gradient(90deg, #8b5cf6, #7c3aed);
            border-radius: 4px;
            transition: width 1s ease;
        }

        /* Styles pour l'analyse PMR */
        .pmr-summary {
            display: flex;
            gap: 40px;
            align-items: center;
            margin-bottom: 30px;
            background: linear-gradient(145deg, #f0fdf4 0%, #dcfce7 100%);
            border-radius: 20px;
            padding: 30px;
        }

        .compliance-score {
            text-align: center;
        }

        .score-circle {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: conic-gradient(#10b981 0deg, #10b981 var(--score-angle, 0deg), #e5e7eb var(--score-angle, 0deg));
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 15px;
            position: relative;
        }

        .score-circle::before {
            content: '';
            width: 80px;
            height: 80px;
            background: white;
            border-radius: 50%;
            position: absolute;
        }

        .score-value {
            font-size: 24px;
            font-weight: 800;
            color: #10b981;
            z-index: 1;
        }

        .score-label {
            font-size: 14px;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .score-status {
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .score-status.excellent {
            background: rgba(16, 185, 129, 0.2);
            color: #10b981;
        }

        .score-status.good {
            background: rgba(245, 158, 11, 0.2);
            color: #f59e0b;
        }

        .score-status.fair {
            background: rgba(239, 68, 68, 0.2);
            color: #ef4444;
        }

        .score-status.poor {
            background: rgba(239, 68, 68, 0.3);
            color: #dc2626;
        }

        .pmr-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            flex: 1;
        }

        .stat-item {
            text-align: center;
            background: white;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .stat-value {
            font-size: 28px;
            font-weight: 800;
            color: #10b981;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 14px;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .pmr-details, .pmr-recommendations {
            margin-bottom: 30px;
        }

        .checks-list {
            display: grid;
            gap: 15px;
            margin-top: 15px;
        }

        .check-item {
            display: flex;
            gap: 15px;
            padding: 20px;
            border-radius: 10px;
            background: #f8fafc;
        }

        .check-item.passed {
            border-left: 4px solid #10b981;
            background: rgba(16, 185, 129, 0.05);
        }

        .check-item.failed {
            border-left: 4px solid #ef4444;
            background: rgba(239, 68, 68, 0.05);
        }

        .check-icon {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
        }

        .check-item.passed .check-icon {
            background: #10b981;
        }

        .check-item.failed .check-icon {
            background: #ef4444;
        }

        .check-content {
            flex: 1;
        }

        .check-name {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 5px;
        }

        .check-description {
            color: #6b7280;
            font-size: 14px;
            margin-bottom: 8px;
        }

        .check-recommendation {
            color: #3b82f6;
            font-size: 13px;
            font-style: italic;
        }

        .recommendations {
            display: grid;
            gap: 10px;
            margin-top: 15px;
        }

        .recommendation-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 15px;
            background: #f8fafc;
            border-radius: 8px;
            border-left: 3px solid #3b82f6;
        }

        .recommendation-item i {
            color: #3b82f6;
        }

        /* Spinner de chargement */
        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid #e5e7eb;
            border-top: 4px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Styles pour la détection automatique de fichier */
        .auto-detected-file {
            margin: 40px 0;
            text-align: center;
        }

        .detection-status {
            margin-bottom: 30px;
        }

        .status-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--success-neon), var(--primary-neon));
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 32px;
            color: white;
            box-shadow: var(--glow-success);
            animation: successPulse 2s infinite;
        }

        @keyframes successPulse {
            0%, 100% { transform: scale(1); box-shadow: var(--glow-success); }
            50% { transform: scale(1.05); box-shadow: 0 0 30px rgba(0, 255, 136, 0.8); }
        }

        .detection-status h3 {
            color: var(--success-neon);
            font-size: 24px;
            margin: 0;
            text-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
        }

        .file-info-card {
            background: var(--bg-glass);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            border: 1px solid rgba(0, 255, 136, 0.3);
            padding: 30px;
            margin: 30px auto;
            max-width: 600px;
            text-align: left;
        }

        .file-info-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .file-info-item:last-child {
            border-bottom: none;
        }

        .file-info-item i {
            width: 20px;
            color: var(--success-neon);
            font-size: 16px;
        }

        .info-label {
            color: var(--text-secondary);
            font-weight: 600;
            min-width: 80px;
        }

        .info-value {
            color: var(--text-primary);
            font-family: 'JetBrains Mono', monospace;
            flex: 1;
        }
    </style>
</head>
<body>
    <div class="mission-control">
        <!-- Header futuriste -->
        <header class="control-header">
            <div class="logo-section">
                <div class="logo-icon">
                    <i class="fas fa-cube"></i>
                </div>
                <div class="logo-text">
                    <div class="logo-title">BIMEX 2.0</div>
                    <div class="logo-subtitle">Mission Control</div>
                </div>
            </div>
            
            <div class="system-status">
                <div class="status-indicator">
                    <div class="status-dot"></div>
                    <span>SYSTÈME OPÉRATIONNEL</span>
                </div>
                <div class="status-indicator">
                    <i class="fas fa-wifi"></i>
                    <span>IA CONNECTÉE</span>
                </div>
                <div class="status-indicator">
                    <i class="fas fa-clock"></i>
                    <span id="current-time">--:--:--</span>
                </div>
            </div>
        </header>

        <!-- Zone principale -->
        <main class="control-main">
            <!-- Sidebar de navigation -->
            <aside class="control-sidebar">
                <h3 class="sidebar-title">Séquence de Mission</h3>
                <ul class="mission-steps">
                    <li class="mission-step">
                        <button class="step-button active" data-step="upload">
                            <i class="fas fa-upload step-icon"></i>
                            <span>Chargement Modèle</span>
                            <div class="step-status"></div>
                        </button>
                    </li>
                    <li class="mission-step">
                        <button class="step-button" data-step="analyze">
                            <i class="fas fa-search step-icon"></i>
                            <span>Analyse Complète</span>
                            <div class="step-status"></div>
                        </button>
                    </li>
                    <li class="mission-step">
                        <button class="step-button" data-step="anomalies">
                            <i class="fas fa-exclamation-triangle step-icon"></i>
                            <span>Détection Anomalies</span>
                            <div class="step-status"></div>
                        </button>
                    </li>
                    <li class="mission-step">
                        <button class="step-button" data-step="classification">
                            <i class="fas fa-brain step-icon"></i>
                            <span>Classification IA</span>
                            <div class="step-status"></div>
                        </button>
                    </li>
                    <li class="mission-step">
                        <button class="step-button" data-step="costs">
                            <i class="fas fa-coins step-icon"></i>
                            <span>Prédiction Coûts</span>
                            <div class="step-status"></div>
                        </button>
                    </li>
                    <li class="mission-step">
                        <button class="step-button" data-step="environment">
                            <i class="fas fa-leaf step-icon"></i>
                            <span>Analyse Environnementale</span>
                            <div class="step-status"></div>
                        </button>
                    </li>
                    <li class="mission-step">
                        <button class="step-button" data-step="optimization">
                            <i class="fas fa-magic step-icon"></i>
                            <span>Optimisation IA</span>
                            <div class="step-status"></div>
                        </button>
                    </li>
                    <li class="mission-step">
                        <button class="step-button" data-step="report">
                            <i class="fas fa-file-pdf step-icon"></i>
                            <span>Rapport Final</span>
                            <div class="step-status"></div>
                        </button>
                    </li>
                </ul>

                <!-- Actions d'Analyse Complètes -->
                <div class="analysis-actions" style="margin-top: 30px; padding-top: 20px; border-top: 1px solid rgba(255,255,255,0.1);">
                    <h4 style="color: var(--text-secondary); font-size: 12px; text-transform: uppercase; letter-spacing: 1px; margin-bottom: 15px;">Analyses Avancées</h4>

                    <button class="analysis-action-btn" onclick="performCompleteAnalysis()" title="Analyse Complète" data-cache="complete-analysis">
                        <i class="fas fa-chart-bar"></i>
                        <span>Analyse Complète</span>
                        <div class="cache-indicator" id="cache-complete-analysis"></div>
                    </button>

                    <button class="analysis-action-btn" onclick="performClassification()" title="Classification IA" data-cache="classification">
                        <i class="fas fa-brain"></i>
                        <span>Classification IA</span>
                        <div class="cache-indicator" id="cache-classification"></div>
                    </button>

                    <button class="analysis-action-btn" onclick="performPMRAnalysis()" title="Analyse PMR" data-cache="pmr-analysis">
                        <i class="fas fa-wheelchair"></i>
                        <span>Analyse PMR</span>
                        <div class="cache-indicator" id="cache-pmr-analysis"></div>
                    </button>

                    <button class="analysis-action-btn" onclick="performAIAssistant()" title="Assistant IA" data-cache="ai-assistant">
                        <i class="fas fa-robot"></i>
                        <span>Assistant IA</span>
                        <div class="cache-indicator" id="cache-ai-assistant"></div>
                    </button>

                    <button class="analysis-action-btn" onclick="performCostPrediction()" title="Prédiction des Coûts IA" data-cache="cost-prediction">
                        <i class="fas fa-coins"></i>
                        <span>Prédiction Coûts IA</span>
                        <div class="cache-indicator" id="cache-cost-prediction"></div>
                    </button>

                    <button class="analysis-action-btn" onclick="performEnvironmentalAnalysis()" title="Analyse Environnementale" data-cache="environmental">
                        <i class="fas fa-leaf"></i>
                        <span>Analyse Environnementale</span>
                        <div class="cache-indicator" id="cache-environmental"></div>
                    </button>

                    <button class="analysis-action-btn" onclick="performOptimization()" title="Optimisation Automatique IA" data-cache="optimization">
                        <i class="fas fa-magic"></i>
                        <span>Optimisation IA</span>
                        <div class="cache-indicator" id="cache-optimization"></div>
                    </button>

                    <button class="analysis-action-btn" onclick="performAnomalyDetection()" title="Détection d'Anomalies" data-cache="anomaly-detection">
                        <i class="fas fa-shield-alt"></i>
                        <span>Détection Anomalies</span>
                        <div class="cache-indicator" id="cache-anomaly-detection"></div>
                    </button>

                    <button class="analysis-action-btn" onclick="generateAdvancedReport()" title="Rapport PDF" data-cache="report">
                        <i class="fas fa-file-pdf"></i>
                        <span>Rapport PDF</span>
                        <div class="cache-indicator" id="cache-report"></div>
                    </button>
                </div>
            </aside>

            <!-- Panel principal -->
            <section class="mission-panel" id="mission-content">
                <!-- Le contenu sera injecté ici dynamiquement -->
            </section>

            <!-- Panel d'informations -->
            <aside class="info-panel">
                <div class="info-widget">
                    <h4 class="widget-title">Statut Projet</h4>
                    <div id="project-status">
                        <div class="metric-item">
                            <span class="metric-name">Projet</span>
                            <span class="metric-value">En attente</span>
                        </div>
                        <div class="metric-item">
                            <span class="metric-name">Étapes</span>
                            <span class="metric-value">0/8</span>
                        </div>
                        <div class="metric-item">
                            <span class="metric-name">Progression</span>
                            <span class="metric-value">0%</span>
                        </div>
                    </div>
                </div>

                <div class="info-widget">
                    <h4 class="widget-title">Métriques Temps Réel</h4>
                    <div id="real-time-metrics">
                        <!-- Métriques seront mises à jour par JavaScript -->
                    </div>
                </div>

                <div class="info-widget">
                    <h4 class="widget-title">Assistant IA</h4>
                    <div id="ai-assistant">
                        <div class="ai-status">
                            <div class="ai-avatar">
                                <i class="fas fa-robot"></i>
                            </div>
                            <div class="ai-message">
                                <div class="ai-name">BIMEX Assistant</div>
                                <div class="ai-text">Prêt à vous assister dans votre analyse BIM. Chargez un modèle pour commencer !</div>
                            </div>
                        </div>
                    </div>
                </div>
            </aside>
        </main>
    </div>

    <script>
        // Horloge temps réel
        function updateTime() {
            const now = new Date();
            document.getElementById('current-time').textContent = now.toLocaleTimeString('fr-FR');
        }
        setInterval(updateTime, 1000);
        updateTime();

        // Navigation entre les étapes
        document.querySelectorAll('.step-button').forEach(button => {
            button.addEventListener('click', function() {
                const step = this.dataset.step;
                switchToStep(step);
            });
        });

        function switchToStep(step) {
            // Mettre à jour l'état actif
            document.querySelectorAll('.step-button').forEach(btn => btn.classList.remove('active'));
            document.querySelector(`[data-step="${step}"]`).classList.add('active');
            
            // Charger le contenu de l'étape
            loadStepContent(step);
        }

        function loadStepContent(step) {
            const content = document.getElementById('mission-content');

            // Contenu selon l'étape avec animations et interactivité
            const stepContents = {
                upload: createUploadInterface(),
                analyze: createAnalysisInterface(),
                anomalies: createAnomaliesInterface(),
                classification: createClassificationInterface(),
                costs: createCostsInterface(),
                environment: createEnvironmentInterface(),
                optimization: createOptimizationInterface(),
                report: createReportInterface()
            };

            // Animation de transition
            content.style.opacity = '0';
            content.style.transform = 'translateY(20px)';

            setTimeout(() => {
                content.innerHTML = stepContents[step] || '<div>Contenu en cours de développement...</div>';
                content.style.opacity = '1';
                content.style.transform = 'translateY(0)';
            }, 200);
        }

        // Interface de chargement futuriste avec détection automatique
        function createUploadInterface() {
            // Vérifier si nous sommes en mode automatique
            const urlParams = new URLSearchParams(window.location.search);
            const project = urlParams.get('project');
            const auto = urlParams.get('auto') === 'true';
            const fileDetected = urlParams.get('file_detected') === 'true';

            if (project && auto && fileDetected) {
                // Mode automatique - fichier détecté
                return `
                    <div class="step-content">
                        <div class="step-header">
                            <h2 class="step-title">
                                <i class="fas fa-rocket"></i>
                                Mission Initialisée Automatiquement
                            </h2>
                            <p class="step-subtitle">Fichier BIM détecté et prêt pour l'analyse intelligente</p>
                        </div>

                        <div class="auto-detected-file">
                            <div class="detection-status">
                                <div class="status-icon">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                                <h3>✅ Fichier Détecté Automatiquement</h3>
                            </div>

                            <div class="file-info-card">
                                <div class="file-info-item">
                                    <i class="fas fa-file"></i>
                                    <span class="info-label">Fichier:</span>
                                    <span class="info-value" id="detected-filename">${project}.ifc</span>
                                </div>
                                <div class="file-info-item">
                                    <i class="fas fa-cube"></i>
                                    <span class="info-label">Projet:</span>
                                    <span class="info-value" id="detected-project">${project}</span>
                                </div>
                                <div class="file-info-item">
                                    <i class="fas fa-folder"></i>
                                    <span class="info-label">Chemin:</span>
                                    <span class="info-value" id="detected-path">data/projects/${project}/models/</span>
                                </div>
                                <div class="file-info-item">
                                    <i class="fas fa-link"></i>
                                    <span class="info-label">Source:</span>
                                    <span class="info-value">XeoKit BIM Viewer</span>
                                </div>
                            </div>
                        </div>

                        <div class="mission-controls">
                            <button class="control-btn primary" id="start-mission-btn" onclick="startMission()">
                                <i class="fas fa-rocket"></i>
                                Démarrer l'Analyse IA
                            </button>
                            <button class="control-btn secondary" onclick="resetSelection()">
                                <i class="fas fa-redo"></i>
                                Réinitialiser (Test)
                            </button>
                        </div>
                    </div>
                `;
            } else {
                // Mode manuel - sélection de projet
                return `
                    <div class="step-content">
                        <div class="step-header">
                            <h2 class="step-title">
                                <i class="fas fa-rocket"></i>
                                Initialisation de Mission
                            </h2>
                            <p class="step-subtitle">Sélectionnez votre projet BIM pour commencer l'analyse intelligente</p>
                        </div>

                        <div class="project-selector-main">
                            <h3>Sélectionnez un Projet BIM</h3>
                            <div class="project-dropdown">
                                <select id="project-select" onchange="onProjectSelected()">
                                    <option value="">-- Choisir un projet --</option>
                                </select>
                            </div>
                        </div>

                        <div class="upload-zone" id="upload-zone" style="display: none;">
                            <div class="upload-visual">
                                <div class="upload-icon">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                </div>
                                <div class="upload-text">
                                    <h3>Glissez votre fichier IFC ici</h3>
                                    <p>ou cliquez pour sélectionner</p>
                                    <small>Formats supportés: .ifc, .ifczip (max 500MB)</small>
                                </div>
                            </div>
                            <input type="file" id="file-input" accept=".ifc,.ifczip" hidden>
                        </div>

                        <div class="mission-controls">
                            <button class="control-btn primary" id="start-mission-btn" onclick="startMission()" disabled>
                                <i class="fas fa-play"></i>
                                Démarrer l'Analyse IA
                            </button>
                            <button class="control-btn secondary" onclick="toggleUploadMode()">
                                <i class="fas fa-upload"></i>
                                Nouveau Fichier
                            </button>
                        </div>
                    </div>
                `;
            }
        }

        // Interface d'analyse avec visualisations temps réel
        function createAnalysisInterface() {
            return `
                <div class="step-content">
                    <div class="step-header">
                        <h2 class="step-title">
                            <i class="fas fa-search"></i>
                            Analyse Structurelle Avancée
                        </h2>
                        <p class="step-subtitle">Extraction intelligente des métriques et caractéristiques du modèle</p>
                    </div>

                    <div class="analysis-dashboard">
                        <div class="metrics-grid">
                            <div class="metric-card neon-blue">
                                <div class="metric-icon"><i class="fas fa-cube"></i></div>
                                <div class="metric-data">
                                    <div class="metric-value" id="total-elements">---</div>
                                    <div class="metric-label">Éléments BIM</div>
                                </div>
                                <div class="metric-trend">
                                    <i class="fas fa-arrow-up"></i>
                                </div>
                            </div>

                            <div class="metric-card neon-green">
                                <div class="metric-icon"><i class="fas fa-expand-arrows-alt"></i></div>
                                <div class="metric-data">
                                    <div class="metric-value" id="total-area">---</div>
                                    <div class="metric-label">Surface (m²)</div>
                                </div>
                                <div class="metric-trend">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                            </div>

                            <div class="metric-card neon-purple">
                                <div class="metric-icon"><i class="fas fa-layer-group"></i></div>
                                <div class="metric-data">
                                    <div class="metric-value" id="total-volume">---</div>
                                    <div class="metric-label">Volume (m³)</div>
                                </div>
                                <div class="metric-trend">
                                    <i class="fas fa-cube"></i>
                                </div>
                            </div>

                            <div class="metric-card neon-orange">
                                <div class="metric-icon"><i class="fas fa-palette"></i></div>
                                <div class="metric-data">
                                    <div class="metric-value" id="total-materials">---</div>
                                    <div class="metric-label">Matériaux</div>
                                </div>
                                <div class="metric-trend">
                                    <i class="fas fa-industry"></i>
                                </div>
                            </div>
                        </div>

                        <div class="analysis-progress">
                            <div class="progress-header">
                                <h3>Progression de l'Analyse</h3>
                                <span class="progress-percentage">0%</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" id="analysis-progress"></div>
                            </div>
                            <div class="progress-status" id="analysis-status">En attente...</div>
                        </div>

                        <div class="analysis-visualization">
                            <canvas id="analysis-chart" width="800" height="400"></canvas>
                        </div>
                    </div>

                    <div class="mission-controls">
                        <button class="control-btn primary" onclick="startAnalysis()">
                            <i class="fas fa-play"></i>
                            Lancer l'Analyse
                        </button>
                        <button class="control-btn secondary" onclick="viewDetails()">
                            <i class="fas fa-chart-bar"></i>
                            Voir Détails
                        </button>
                    </div>
                </div>
            `;
        }

        // Interface de détection d'anomalies avec IA
        function createAnomaliesInterface() {
            return `
                <div class="step-content">
                    <div class="step-header">
                        <h2 class="step-title">
                            <i class="fas fa-shield-alt"></i>
                            Détection d'Anomalies IA
                        </h2>
                        <p class="step-subtitle">Identification intelligente des conflits et problèmes potentiels</p>
                    </div>

                    <div class="anomalies-scanner">
                        <div class="scanner-visual">
                            <div class="radar-container">
                                <div class="radar-sweep"></div>
                                <div class="radar-grid"></div>
                                <div class="radar-center"></div>
                            </div>
                            <div class="scanner-status">
                                <h3>Scanner IA Actif</h3>
                                <p>Analyse en cours...</p>
                            </div>
                        </div>

                        <div class="anomalies-results">
                            <div class="severity-grid">
                                <div class="severity-card critical">
                                    <div class="severity-icon"><i class="fas fa-exclamation-triangle"></i></div>
                                    <div class="severity-count" id="critical-count">0</div>
                                    <div class="severity-label">Critiques</div>
                                </div>
                                <div class="severity-card high">
                                    <div class="severity-icon"><i class="fas fa-exclamation-circle"></i></div>
                                    <div class="severity-count" id="high-count">0</div>
                                    <div class="severity-label">Élevées</div>
                                </div>
                                <div class="severity-card medium">
                                    <div class="severity-icon"><i class="fas fa-info-circle"></i></div>
                                    <div class="severity-count" id="medium-count">0</div>
                                    <div class="severity-label">Moyennes</div>
                                </div>
                                <div class="severity-card low">
                                    <div class="severity-icon"><i class="fas fa-check-circle"></i></div>
                                    <div class="severity-count" id="low-count">0</div>
                                    <div class="severity-label">Faibles</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mission-controls">
                        <button class="control-btn primary" onclick="startAnomalyDetection()">
                            <i class="fas fa-search"></i>
                            Scanner les Anomalies
                        </button>
                        <button class="control-btn secondary" onclick="viewAnomaliesReport()">
                            <i class="fas fa-list"></i>
                            Rapport Détaillé
                        </button>
                    </div>
                </div>
            `;
        }

        // Interfaces pour les autres étapes
        function createClassificationInterface() {
            return `
                <div class="step-content">
                    <div class="step-header">
                        <h2 class="step-title">
                            <i class="fas fa-brain"></i>
                            Classification IA Avancée
                        </h2>
                        <p class="step-subtitle">Identification automatique du type de bâtiment par deep learning</p>
                    </div>

                    <div class="ai-brain-visual">
                        <div class="neural-network">
                            <div class="neural-layer input-layer">
                                <div class="neuron active"></div>
                                <div class="neuron active"></div>
                                <div class="neuron active"></div>
                                <div class="neuron active"></div>
                            </div>
                            <div class="neural-layer hidden-layer">
                                <div class="neuron"></div>
                                <div class="neuron"></div>
                                <div class="neuron"></div>
                            </div>
                            <div class="neural-layer output-layer">
                                <div class="neuron"></div>
                                <div class="neuron"></div>
                            </div>
                        </div>
                        <div class="classification-result">
                            <h3>Résultat de Classification</h3>
                            <div class="building-type" id="building-type">En cours d'analyse...</div>
                            <div class="confidence-meter">
                                <div class="confidence-bar" id="confidence-bar"></div>
                                <span class="confidence-text" id="confidence-text">0%</span>
                            </div>
                        </div>
                    </div>

                    <div class="mission-controls">
                        <button class="control-btn primary" onclick="startClassification()">
                            <i class="fas fa-brain"></i>
                            Analyser avec IA
                        </button>
                    </div>
                </div>
            `;
        }

        function createCostsInterface() {
            return `
                <div class="step-content">
                    <div class="step-header">
                        <h2 class="step-title">
                            <i class="fas fa-coins"></i>
                            Prédiction Intelligente des Coûts
                        </h2>
                        <p class="step-subtitle">Estimation automatique basée sur l'analyse du modèle IFC</p>
                    </div>

                    <div class="costs-dashboard">
                        <div class="cost-summary">
                            <div class="cost-main">
                                <div class="cost-value" id="total-cost">€ ---</div>
                                <div class="cost-label">Coût Total Estimé</div>
                            </div>
                            <div class="cost-details">
                                <div class="cost-item">
                                    <span>Coût par m²</span>
                                    <span id="cost-per-m2">€ ---</span>
                                </div>
                                <div class="cost-item">
                                    <span>Confiance IA</span>
                                    <span id="cost-confidence">---%</span>
                                </div>
                            </div>
                        </div>

                        <div class="cost-breakdown">
                            <h3>Répartition des Coûts</h3>
                            <div class="breakdown-chart" id="cost-chart">
                                <!-- Graphique circulaire des coûts -->
                            </div>
                        </div>
                    </div>

                    <div class="mission-controls">
                        <button class="control-btn primary" onclick="predictCosts()">
                            <i class="fas fa-calculator"></i>
                            Calculer les Coûts
                        </button>
                    </div>
                </div>
            `;
        }

        function createEnvironmentInterface() {
            return `
                <div class="step-content">
                    <div class="step-header">
                        <h2 class="step-title">
                            <i class="fas fa-leaf"></i>
                            Analyse Environnementale & Durabilité
                        </h2>
                        <p class="step-subtitle">Évaluation de l'impact environnemental et suggestions d'optimisation</p>
                    </div>

                    <div class="environment-dashboard">
                        <div class="sustainability-score">
                            <div class="score-circle">
                                <svg class="score-ring" width="120" height="120">
                                    <circle cx="60" cy="60" r="50" stroke="rgba(0,255,136,0.2)" stroke-width="8" fill="none"/>
                                    <circle cx="60" cy="60" r="50" stroke="var(--success-neon)" stroke-width="8" fill="none"
                                            stroke-dasharray="314" stroke-dashoffset="314" id="score-progress"/>
                                </svg>
                                <div class="score-text">
                                    <div class="score-value" id="sustainability-score">-</div>
                                    <div class="score-max">/10</div>
                                </div>
                            </div>
                            <h3>Score de Durabilité</h3>
                        </div>

                        <div class="environmental-metrics">
                            <div class="env-metric">
                                <div class="env-icon"><i class="fas fa-cloud"></i></div>
                                <div class="env-data">
                                    <div class="env-value" id="carbon-footprint">--- kg</div>
                                    <div class="env-label">Empreinte Carbone</div>
                                </div>
                            </div>
                            <div class="env-metric">
                                <div class="env-icon"><i class="fas fa-bolt"></i></div>
                                <div class="env-data">
                                    <div class="env-value" id="energy-class">-</div>
                                    <div class="env-label">Classe Énergétique</div>
                                </div>
                            </div>
                            <div class="env-metric">
                                <div class="env-icon"><i class="fas fa-sun"></i></div>
                                <div class="env-data">
                                    <div class="env-value" id="solar-potential">---%</div>
                                    <div class="env-label">Potentiel Solaire</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mission-controls">
                        <button class="control-btn primary" onclick="analyzeEnvironment()">
                            <i class="fas fa-leaf"></i>
                            Analyser l'Impact
                        </button>
                    </div>
                </div>
            `;
        }

        function createOptimizationInterface() {
            return `
                <div class="step-content">
                    <div class="step-header">
                        <h2 class="step-title">
                            <i class="fas fa-magic"></i>
                            Optimisation Automatique IA
                        </h2>
                        <p class="step-subtitle">Algorithmes génétiques pour l'optimisation multi-objectifs</p>
                    </div>

                    <div class="optimization-lab">
                        <div class="genetic-algorithm">
                            <h3>Algorithme Génétique en Action</h3>
                            <div class="generations-display">
                                <div class="generation" id="current-generation">
                                    <div class="generation-number">Génération: <span>0</span></div>
                                    <div class="fitness-score">Fitness: <span>0%</span></div>
                                </div>
                                <div class="evolution-progress">
                                    <div class="evolution-bar" id="evolution-progress"></div>
                                </div>
                            </div>
                        </div>

                        <div class="optimization-results">
                            <div class="pareto-solutions">
                                <h3>Solutions Pareto Optimales</h3>
                                <div class="solutions-grid" id="pareto-grid">
                                    <!-- Solutions seront générées ici -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mission-controls">
                        <button class="control-btn primary" onclick="startOptimization()">
                            <i class="fas fa-rocket"></i>
                            Optimiser avec IA
                        </button>
                    </div>
                </div>
            `;
        }

        function createReportInterface() {
            return `
                <div class="step-content">
                    <div class="step-header">
                        <h2 class="step-title">
                            <i class="fas fa-file-pdf"></i>
                            Rapport de Mission Complet
                        </h2>
                        <p class="step-subtitle">Génération automatique du rapport professionnel</p>
                    </div>

                    <div class="report-generator">
                        <div class="report-preview">
                            <div class="document-icon">
                                <i class="fas fa-file-pdf"></i>
                            </div>
                            <h3>Rapport d'Analyse BIM</h3>
                            <p>Document professionnel complet avec toutes les analyses effectuées</p>
                        </div>

                        <div class="report-sections">
                            <div class="section-item completed">
                                <i class="fas fa-check-circle"></i>
                                <span>Analyse Structurelle</span>
                            </div>
                            <div class="section-item completed">
                                <i class="fas fa-check-circle"></i>
                                <span>Détection d'Anomalies</span>
                            </div>
                            <div class="section-item completed">
                                <i class="fas fa-check-circle"></i>
                                <span>Classification IA</span>
                            </div>
                            <div class="section-item completed">
                                <i class="fas fa-check-circle"></i>
                                <span>Prédiction des Coûts</span>
                            </div>
                            <div class="section-item completed">
                                <i class="fas fa-check-circle"></i>
                                <span>Analyse Environnementale</span>
                            </div>
                            <div class="section-item completed">
                                <i class="fas fa-check-circle"></i>
                                <span>Optimisation IA</span>
                            </div>
                        </div>
                    </div>

                    <div class="mission-controls">
                        <button class="control-btn primary" onclick="generateReport()">
                            <i class="fas fa-download"></i>
                            Générer le Rapport
                        </button>
                        <button class="control-btn secondary" onclick="previewReport()">
                            <i class="fas fa-eye"></i>
                            Aperçu
                        </button>
                    </div>
                </div>
            `;
        }

        // Fonctions d'interaction pour les différentes étapes
        function startMission() {
            if (!selectedProject) {
                alert('⚠️ Veuillez sélectionner un projet avant de commencer !');
                return;
            }

            console.log(`🚀 Mission démarrée pour le projet: ${selectedProject}`);
            console.log('📁 Fichier détecté:', currentFile);

            markStepCompleted('upload');
            switchToStep('analyze');

            // Démarrer automatiquement l'analyse
            setTimeout(() => {
                startAnalysis();
            }, 1000);
        }

        function loadDemo() {
            console.log('👁️ Mode démonstration activé');
            // Simuler le chargement d'un projet de démonstration
            setTimeout(() => {
                startMission();
            }, 1000);
        }

        async function startAnalysis() {
            if (!selectedProject) return;

            console.log('🔍 Analyse démarrée pour:', selectedProject);

            try {
                // Lancer l'analyse réelle
                const response = await fetch(`http://localhost:8000/analyze/${selectedProject}`);
                const analysisData = await response.json();

                // Mettre à jour les métriques avec les vraies données
                updateAnalysisMetrics(analysisData);

                // Simuler la progression
                simulateProgress('analysis-progress', () => {
                    markStepCompleted('analyze');
                    switchToStep('anomalies');
                });

            } catch (error) {
                console.error('Erreur lors de l\'analyse:', error);
                // Fallback vers simulation
                simulateProgress('analysis-progress', () => {
                    markStepCompleted('analyze');
                    switchToStep('anomalies');
                });
            }
        }

        // Mettre à jour les métriques d'analyse avec les vraies données
        function updateAnalysisMetrics(data) {
            if (data) {
                const totalElements = document.getElementById('total-elements');
                const totalArea = document.getElementById('total-area');
                const totalVolume = document.getElementById('total-volume');
                const totalMaterials = document.getElementById('total-materials');

                if (totalElements) totalElements.textContent = data.total_elements || '---';
                if (totalArea) totalArea.textContent = data.total_floor_area ? Math.round(data.total_floor_area) + ' m²' : '---';
                if (totalVolume) totalVolume.textContent = data.total_volume ? Math.round(data.total_volume) + ' m³' : '---';
                if (totalMaterials) totalMaterials.textContent = data.materials_count || '---';
            }
        }

        async function startAnomalyDetection() {
            if (!selectedProject) return;

            console.log('🛡️ Détection d\'anomalies démarrée pour:', selectedProject);

            try {
                // Lancer la détection d'anomalies réelle
                const response = await fetch(`http://localhost:8000/detect-anomalies/${selectedProject}`);
                const anomaliesData = await response.json();

                // Mettre à jour les compteurs avec les vraies données
                updateAnomaliesCount(anomaliesData);

                setTimeout(() => {
                    markStepCompleted('anomalies');
                    switchToStep('classification');
                }, 3000);

            } catch (error) {
                console.error('Erreur lors de la détection d\'anomalies:', error);
                // Fallback vers simulation
                simulateAnomalyScanning(() => {
                    markStepCompleted('anomalies');
                    switchToStep('classification');
                });
            }
        }

        // Mettre à jour les compteurs d'anomalies avec les vraies données
        function updateAnomaliesCount(data) {
            if (data && data.anomalies_by_severity) {
                const criticalCount = document.getElementById('critical-count');
                const highCount = document.getElementById('high-count');
                const mediumCount = document.getElementById('medium-count');
                const lowCount = document.getElementById('low-count');

                if (criticalCount) criticalCount.textContent = data.anomalies_by_severity.critical || 0;
                if (highCount) highCount.textContent = data.anomalies_by_severity.high || 0;
                if (mediumCount) mediumCount.textContent = data.anomalies_by_severity.medium || 0;
                if (lowCount) lowCount.textContent = data.anomalies_by_severity.low || 0;
            }
        }

        function startClassification() {
            console.log('🧠 Classification IA démarrée');
            simulateNeuralNetwork(() => {
                markStepCompleted('classification');
                switchToStep('costs');
            });
        }

        function predictCosts() {
            console.log('💰 Prédiction des coûts démarrée');
            simulateCostCalculation(() => {
                markStepCompleted('costs');
                switchToStep('environment');
            });
        }

        function analyzeEnvironment() {
            console.log('🌱 Analyse environnementale démarrée');
            simulateEnvironmentalAnalysis(() => {
                markStepCompleted('environment');
                switchToStep('optimization');
            });
        }

        function startOptimization() {
            console.log('⚡ Optimisation IA démarrée');
            simulateGeneticAlgorithm(() => {
                markStepCompleted('optimization');
                switchToStep('report');
            });
        }

        function generateReport() {
            console.log('📄 Génération du rapport');
            markStepCompleted('report');
            // Rediriger vers la génération de rapport réelle
            window.location.href = '/generate-html-report?project=demo&auto=true';
        }

        function previewReport() {
            console.log('👁️ Aperçu du rapport');
            // Ouvrir l'aperçu dans une nouvelle fenêtre
            window.open('/generate-html-report?project=demo&preview=true', '_blank');
        }

        // Fonctions utilitaires
        function markStepCompleted(step) {
            const button = document.querySelector(`[data-step="${step}"]`);
            if (button) {
                button.classList.add('completed');
                button.classList.remove('active');
            }
        }

        function simulateProgress(elementId, callback) {
            const progressBar = document.getElementById(elementId);
            const statusElement = document.getElementById('analysis-status');
            const percentageElement = document.querySelector('.progress-percentage');

            if (!progressBar) return;

            let progress = 0;
            const interval = setInterval(() => {
                progress += Math.random() * 15;
                if (progress > 100) progress = 100;

                progressBar.style.width = progress + '%';
                if (percentageElement) percentageElement.textContent = Math.round(progress) + '%';

                // Messages de statut dynamiques
                if (statusElement) {
                    const messages = [
                        'Extraction des éléments IFC...',
                        'Analyse des géométries...',
                        'Calcul des surfaces et volumes...',
                        'Identification des matériaux...',
                        'Finalisation de l\'analyse...'
                    ];
                    const messageIndex = Math.floor((progress / 100) * messages.length);
                    statusElement.textContent = messages[Math.min(messageIndex, messages.length - 1)];
                }

                if (progress >= 100) {
                    clearInterval(interval);
                    setTimeout(callback, 500);
                }
            }, 200);
        }

        function simulateAnomalyScanning(callback) {
            // Simuler la détection d'anomalies avec animation radar
            const counts = {
                critical: Math.floor(Math.random() * 5),
                high: Math.floor(Math.random() * 10) + 2,
                medium: Math.floor(Math.random() * 15) + 5,
                low: Math.floor(Math.random() * 20) + 10
            };

            setTimeout(() => {
                document.getElementById('critical-count').textContent = counts.critical;
                document.getElementById('high-count').textContent = counts.high;
                document.getElementById('medium-count').textContent = counts.medium;
                document.getElementById('low-count').textContent = counts.low;

                setTimeout(callback, 2000);
            }, 3000);
        }

        function simulateNeuralNetwork(callback) {
            // Animer le réseau de neurones
            const neurons = document.querySelectorAll('.neuron');
            let index = 0;

            const activateNeuron = () => {
                if (index < neurons.length) {
                    neurons[index].classList.add('active');
                    index++;
                    setTimeout(activateNeuron, 300);
                } else {
                    // Afficher le résultat de classification
                    setTimeout(() => {
                        const buildingTypes = ['Résidentiel', 'Tertiaire', 'Industriel', 'Commercial'];
                        const selectedType = buildingTypes[Math.floor(Math.random() * buildingTypes.length)];
                        const confidence = Math.floor(Math.random() * 30) + 70; // 70-100%

                        document.getElementById('building-type').textContent = selectedType;
                        document.getElementById('confidence-bar').style.width = confidence + '%';
                        document.getElementById('confidence-text').textContent = confidence + '%';

                        setTimeout(callback, 2000);
                    }, 1000);
                }
            };

            activateNeuron();
        }

        function simulateCostCalculation(callback) {
            // Simuler le calcul des coûts
            const totalCost = Math.floor(Math.random() * 500000) + 200000; // 200k-700k€
            const costPerM2 = Math.floor(totalCost / (Math.random() * 200 + 100)); // Surface aléatoire
            const confidence = Math.floor(Math.random() * 20) + 80; // 80-100%

            setTimeout(() => {
                document.getElementById('total-cost').textContent = '€ ' + totalCost.toLocaleString('fr-FR');
                document.getElementById('cost-per-m2').textContent = '€ ' + costPerM2.toLocaleString('fr-FR');
                document.getElementById('cost-confidence').textContent = confidence + '%';

                setTimeout(callback, 2000);
            }, 2000);
        }

        function simulateEnvironmentalAnalysis(callback) {
            // Simuler l'analyse environnementale
            const sustainabilityScore = Math.floor(Math.random() * 4) + 6; // 6-10
            const carbonFootprint = Math.floor(Math.random() * 50000) + 10000; // 10-60 tonnes
            const energyClass = ['A+', 'A', 'B', 'C'][Math.floor(Math.random() * 4)];
            const solarPotential = Math.floor(Math.random() * 40) + 60; // 60-100%

            setTimeout(() => {
                // Animer le score circulaire
                const scoreProgress = document.getElementById('score-progress');
                const circumference = 2 * Math.PI * 50; // rayon = 50
                const offset = circumference - (sustainabilityScore / 10) * circumference;
                scoreProgress.style.strokeDashoffset = offset;

                document.getElementById('sustainability-score').textContent = sustainabilityScore;
                document.getElementById('carbon-footprint').textContent = (carbonFootprint / 1000).toFixed(1) + ' t';
                document.getElementById('energy-class').textContent = energyClass;
                document.getElementById('solar-potential').textContent = solarPotential + '%';

                setTimeout(callback, 3000);
            }, 2000);
        }

        function simulateGeneticAlgorithm(callback) {
            // Simuler l'algorithme génétique
            let generation = 0;
            const maxGenerations = 50;

            const evolve = () => {
                if (generation < maxGenerations) {
                    generation++;
                    const fitness = Math.min(100, generation * 2 + Math.random() * 10);

                    document.querySelector('.generation-number span').textContent = generation;
                    document.querySelector('.fitness-score span').textContent = Math.round(fitness) + '%';
                    document.getElementById('evolution-progress').style.width = (generation / maxGenerations * 100) + '%';

                    setTimeout(evolve, 100);
                } else {
                    setTimeout(callback, 1000);
                }
            };

            evolve();
        }

        // Variables globales
        let selectedProject = null;
        let projectData = null;
        let autoMode = false;
        let currentFile = null;
        let currentPopup = null;

        // Configuration de l'API (copiée de bim_analysis.html)
        const API_BASE = 'http://localhost:8000';

        // Système de cache pour les analyses
        const analysisCache = new Map();
        const cacheTimestamps = new Map();
        const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

        // Charger les projets disponibles
        async function loadAvailableProjects() {
            try {
                const response = await fetch('http://localhost:8000/projects');
                const projects = await response.json();

                const select = document.getElementById('project-select');
                if (select) {
                    select.innerHTML = '<option value="">-- Choisir un projet --</option>';

                    projects.forEach(project => {
                        const option = document.createElement('option');
                        option.value = project.id;
                        option.textContent = `${project.name} (${project.id})`;
                        select.appendChild(option);
                    });
                }
            } catch (error) {
                console.error('Erreur lors du chargement des projets:', error);
            }
        }

        // Gestion de la sélection de projet
        function onProjectSelected() {
            const select = document.getElementById('project-select');
            const startBtn = document.getElementById('start-mission-btn');

            if (select.value) {
                selectedProject = select.value;
                startBtn.disabled = false;
                startBtn.innerHTML = '<i class="fas fa-rocket"></i> Analyser ' + selectedProject;

                // Charger les données du projet
                loadProjectData(selectedProject);
            } else {
                selectedProject = null;
                startBtn.disabled = true;
                startBtn.innerHTML = '<i class="fas fa-play"></i> Démarrer l\'Analyse IA';
            }
        }

        // Charger les données du projet sélectionné
        async function loadProjectData(projectId) {
            try {
                const response = await fetch(`http://localhost:8000/project-info/${projectId}`);
                projectData = await response.json();

                // Mettre à jour le panel d'informations
                updateProjectStatus(projectData);

            } catch (error) {
                console.error('Erreur lors du chargement des données:', error);
            }
        }

        // Mettre à jour le statut du projet
        function updateProjectStatus(data) {
            const statusPanel = document.getElementById('project-status');
            if (statusPanel && data) {
                statusPanel.innerHTML = `
                    <div class="metric-item">
                        <span class="metric-name">Projet</span>
                        <span class="metric-value">${data.name || selectedProject}</span>
                    </div>
                    <div class="metric-item">
                        <span class="metric-name">Éléments</span>
                        <span class="metric-value">${data.total_elements || 'N/A'}</span>
                    </div>
                    <div class="metric-item">
                        <span class="metric-name">Surface</span>
                        <span class="metric-value">${data.total_floor_area ? Math.round(data.total_floor_area) + ' m²' : 'N/A'}</span>
                    </div>
                    <div class="metric-item">
                        <span class="metric-name">Statut</span>
                        <span class="metric-value">Prêt</span>
                    </div>
                `;
            }
        }

        // Toggle mode upload
        function toggleUploadMode() {
            const uploadZone = document.getElementById('upload-zone');
            const projectSelector = document.querySelector('.project-selector-main');

            if (uploadZone.style.display === 'none') {
                uploadZone.style.display = 'block';
                projectSelector.style.display = 'none';
            } else {
                uploadZone.style.display = 'none';
                projectSelector.style.display = 'block';
            }
        }

        // Configuration du mode automatique
        function configureAutoMode() {
            const urlParams = new URLSearchParams(window.location.search);
            const project = urlParams.get('project');
            const auto = urlParams.get('auto') === 'true';
            const fileDetected = urlParams.get('file_detected') === 'true';
            const step = urlParams.get('step');

            console.log('🔍 Paramètres URL détectés:', { project, auto, fileDetected, step });

            if (project && auto && fileDetected) {
                console.log('🚀 Configuration du mode automatique pour:', project);

                // Configurer les variables globales
                selectedProject = project;
                autoMode = true;
                currentFile = {
                    name: `${project}.ifc`,
                    project: project,
                    auto: true,
                    source: 'xeokit',
                    path: `data/projects/${project}/models/model/${project}.ifc`
                };

                // Charger les données du projet
                loadProjectData(project);

                // Mettre à jour le statut
                updateProjectStatus({
                    name: project,
                    total_elements: 'Chargement...',
                    total_floor_area: 'Chargement...'
                });

                return true;
            }

            return false;
        }

        // Fonction de réinitialisation
        function resetSelection() {
            console.log('🔄 Réinitialisation de la sélection');

            // Réinitialiser les variables
            selectedProject = null;
            autoMode = false;
            currentFile = null;

            // Recharger la page sans paramètres
            window.location.href = window.location.pathname;
        }

        // Initialiser avec la première étape
        loadStepContent('upload');

        // Configurer le mode automatique ou charger les projets
        setTimeout(() => {
            if (!configureAutoMode()) {
                // Mode manuel - charger les projets disponibles
                loadAvailableProjects();
            }
        }, 1000);

        // Mise à jour des métriques temps réel dans le panel d'informations
        function updateRealTimeMetrics() {
            const metricsPanel = document.getElementById('real-time-metrics');
            if (metricsPanel) {
                metricsPanel.innerHTML = `
                    <div class="metric-item">
                        <span class="metric-name">CPU IA</span>
                        <span class="metric-value">${Math.floor(Math.random() * 30) + 20}%</span>
                    </div>
                    <div class="metric-item">
                        <span class="metric-name">Mémoire</span>
                        <span class="metric-value">${Math.floor(Math.random() * 40) + 30}%</span>
                    </div>
                    <div class="metric-item">
                        <span class="metric-name">Réseau</span>
                        <span class="metric-value">${Math.floor(Math.random() * 100) + 50} MB/s</span>
                    </div>
                `;
            }
        }

        // Fonctions pour afficher les pop-ups d'analyse
        async function showDetailedAnalysis() {
            if (!selectedProject) {
                alert('⚠️ Veuillez sélectionner un projet d\'abord !');
                return;
            }

            try {
                const response = await fetch(`http://localhost:8000/detailed-analysis/${selectedProject}`);
                const data = await response.json();

                // Créer et afficher le pop-up
                createAnalysisPopup('Analyse Détaillée', data, 'detailed');

            } catch (error) {
                console.error('Erreur:', error);
                alert('❌ Erreur lors de l\'analyse détaillée');
            }
        }

        async function showPMRAnalysis() {
            if (!selectedProject) {
                alert('⚠️ Veuillez sélectionner un projet d\'abord !');
                return;
            }

            try {
                const response = await fetch(`http://localhost:8000/pmr-analysis/${selectedProject}`);
                const data = await response.json();

                createAnalysisPopup('Analyse PMR', data, 'pmr');

            } catch (error) {
                console.error('Erreur:', error);
                alert('❌ Erreur lors de l\'analyse PMR');
            }
        }

        async function showClassification() {
            if (!selectedProject) {
                alert('⚠️ Veuillez sélectionner un projet d\'abord !');
                return;
            }

            try {
                const response = await fetch(`http://localhost:8000/classify/${selectedProject}`);
                const data = await response.json();

                createAnalysisPopup('Classification IA', data, 'classification');

            } catch (error) {
                console.error('Erreur:', error);
                alert('❌ Erreur lors de la classification');
            }
        }

        // Créer un pop-up d'analyse futuriste
        function createAnalysisPopup(title, data, type) {
            const popup = document.createElement('div');
            popup.className = 'analysis-popup';
            popup.innerHTML = `
                <div class="popup-overlay" onclick="closeAnalysisPopup()"></div>
                <div class="popup-content">
                    <div class="popup-header">
                        <h3><i class="fas fa-chart-line"></i> ${title}</h3>
                        <button class="popup-close" onclick="closeAnalysisPopup()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="popup-body">
                        ${formatAnalysisData(data, type)}
                    </div>
                </div>
            `;

            document.body.appendChild(popup);

            // Animation d'entrée
            setTimeout(() => {
                popup.classList.add('active');
            }, 10);
        }

        // Formater les données selon le type d'analyse
        function formatAnalysisData(data, type) {
            if (!data) return '<p>Aucune donnée disponible</p>';

            switch (type) {
                case 'detailed':
                    return `
                        <div class="analysis-grid">
                            <div class="analysis-card">
                                <h4>📊 Éléments BIM</h4>
                                <div class="metric-large">${data.total_elements || 'N/A'}</div>
                            </div>
                            <div class="analysis-card">
                                <h4>📐 Surface Totale</h4>
                                <div class="metric-large">${data.total_floor_area ? Math.round(data.total_floor_area) + ' m²' : 'N/A'}</div>
                            </div>
                            <div class="analysis-card">
                                <h4>📦 Volume</h4>
                                <div class="metric-large">${data.total_volume ? Math.round(data.total_volume) + ' m³' : 'N/A'}</div>
                            </div>
                        </div>
                    `;

                case 'pmr':
                    return `
                        <div class="pmr-analysis">
                            <div class="compliance-score">
                                <h4>🦽 Score de Conformité PMR</h4>
                                <div class="score-circle">
                                    <span class="score-value">${data.compliance_rate ? Math.round(data.compliance_rate) : 'N/A'}%</span>
                                </div>
                            </div>
                            <div class="pmr-details">
                                <h4>Détails de l'Analyse</h4>
                                <p>Analyse de l'accessibilité selon les normes PMR en cours...</p>
                            </div>
                        </div>
                    `;

                case 'classification':
                    return `
                        <div class="classification-result">
                            <h4>🧠 Classification par IA</h4>
                            <div class="building-type-result">
                                <span class="type-label">Type de Bâtiment:</span>
                                <span class="type-value">${data.building_type || 'Non classifié'}</span>
                            </div>
                            <div class="confidence-bar">
                                <span>Confiance: ${data.confidence ? Math.round(data.confidence * 100) : 0}%</span>
                                <div class="bar">
                                    <div class="fill" style="width: ${data.confidence ? data.confidence * 100 : 0}%"></div>
                                </div>
                            </div>
                        </div>
                    `;

                default:
                    return '<p>Données d\'analyse en cours de traitement...</p>';
            }
        }

        // Fermer le pop-up
        function closeAnalysisPopup() {
            const popup = document.querySelector('.analysis-popup');
            if (popup) {
                popup.classList.remove('active');
                setTimeout(() => popup.remove(), 300);
            }
        }

        // ===== SYSTÈME DE CACHE ET UTILITAIRES =====

        // Générer une clé de cache basée sur le projet et le type d'analyse
        function getCacheKey(analysisType = 'default') {
            return `${selectedProject || 'unknown'}_${analysisType}`;
        }

        // Vérifier si les données sont en cache et valides
        function isCacheValid(cacheKey) {
            if (!analysisCache.has(cacheKey)) return false;

            const timestamp = cacheTimestamps.get(cacheKey);
            const now = Date.now();

            return (now - timestamp) < CACHE_DURATION;
        }

        // Sauvegarder dans le cache
        function saveToCache(cacheKey, data) {
            analysisCache.set(cacheKey, data);
            cacheTimestamps.set(cacheKey, Date.now());
            updateCacheIndicator(cacheKey, 'cached');
        }

        // Récupérer du cache
        function getFromCache(cacheKey) {
            return analysisCache.get(cacheKey);
        }

        // Mettre à jour l'indicateur de cache visuel
        function updateCacheIndicator(analysisType, status) {
            const indicator = document.getElementById(`cache-${analysisType}`);
            if (indicator) {
                indicator.className = `cache-indicator ${status}`;
            }

            // Mettre à jour le bouton aussi
            const button = document.querySelector(`[data-cache="${analysisType}"]`);
            if (button) {
                button.className = `analysis-action-btn ${status}`;
            }
        }

        // Fermer le pop-up actuel
        function closeCurrentPopup() {
            if (currentPopup) {
                currentPopup.classList.remove('active');
                setTimeout(() => {
                    if (currentPopup && currentPopup.parentNode) {
                        currentPopup.remove();
                    }
                    currentPopup = null;
                }, 300);
            }
        }

        // Créer un pop-up de base avec le style BIMEX 2.0
        function createBIMEXPopup(title, content, className = '') {
            closeCurrentPopup();

            const popup = document.createElement('div');
            popup.className = `analysis-popup bimex-popup ${className}`;
            popup.innerHTML = `
                <div class="popup-overlay" onclick="closeCurrentPopup()"></div>
                <div class="popup-content">
                    <div class="popup-header">
                        <h3><i class="fas fa-chart-line"></i> ${title}</h3>
                        <button class="popup-close" onclick="closeCurrentPopup()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="popup-body">
                        ${content}
                    </div>
                </div>
            `;

            document.body.appendChild(popup);
            currentPopup = popup;

            // Animation d'entrée
            setTimeout(() => {
                popup.classList.add('active');
            }, 10);

            return popup;
        }

        // Créer un pop-up de chargement
        function createLoadingPopup(title, message = 'Analyse en cours...') {
            const loadingContent = `
                <div class="loading-container">
                    <div class="loading-spinner">
                        <div class="spinner-ring"></div>
                        <div class="spinner-ring"></div>
                        <div class="spinner-ring"></div>
                    </div>
                    <h4>${message}</h4>
                    <p>Veuillez patienter pendant que l'IA traite vos données...</p>
                </div>
            `;

            return createBIMEXPopup(title, loadingContent, 'loading-popup');
        }

        // ===== FONCTIONS D'ANALYSE PRINCIPALES =====

        // 1. Analyse Complète (copiée de bim_analysis.html)
        async function performCompleteAnalysis() {
            if (!selectedProject) {
                alert('⚠️ Veuillez sélectionner un projet d\'abord !');
                return;
            }

            const cacheKey = getCacheKey('complete-analysis');

            // Vérifier le cache
            if (isCacheValid(cacheKey)) {
                console.log('📋 Données d\'analyse complète récupérées du cache');
                const cachedData = getFromCache(cacheKey);
                showAnalysisPopup(cachedData);
                return;
            }

            // Mettre à jour l'indicateur
            updateCacheIndicator('complete-analysis', 'loading');

            // Créer le pop-up de chargement
            showAnalysisPopupWithLoading();

            try {
                let response, result;

                if (currentFile && currentFile.auto && currentFile.source === 'xeokit') {
                    console.log(`🔍 Analyse automatique du projet: ${currentFile.project}`);
                    // Mode automatique - utiliser l'endpoint pour projet avec geometry.ifc
                    response = await fetch(`${API_BASE}/analyze-comprehensive-project/${currentFile.project}`);
                    result = await response.json();
                } else {
                    // Mode manuel avec fichier uploadé
                    const formData = new FormData();
                    formData.append('file', currentFile);

                    response = await fetch(`${API_BASE}/analyze-ifc`, {
                        method: 'POST',
                        body: formData
                    });
                    result = await response.json();
                }

                // Sauvegarder en cache
                saveToCache(cacheKey, result);

                // Afficher les résultats
                updateAnalysisPopup(result);

            } catch (error) {
                console.error('❌ Erreur lors de l\'analyse complète:', error);
                updateCacheIndicator('complete-analysis', 'error');
                updateAnalysisPopupWithError('Erreur lors de l\'analyse complète. Veuillez réessayer.');
            }
        }

        // Afficher le pop-up d'analyse complète
        function showCompleteAnalysisPopup(data) {
            const content = `
                <div class="complete-analysis-content">
                    <div class="analysis-summary">
                        <h4><i class="fas fa-chart-bar"></i> Résumé de l'Analyse</h4>
                        <div class="summary-grid">
                            <div class="summary-card">
                                <div class="summary-icon"><i class="fas fa-cube"></i></div>
                                <div class="summary-data">
                                    <div class="summary-value">${data.total_elements || 'N/A'}</div>
                                    <div class="summary-label">Éléments BIM</div>
                                </div>
                            </div>
                            <div class="summary-card">
                                <div class="summary-icon"><i class="fas fa-expand-arrows-alt"></i></div>
                                <div class="summary-data">
                                    <div class="summary-value">${data.total_floor_area ? Math.round(data.total_floor_area) + ' m²' : 'N/A'}</div>
                                    <div class="summary-label">Surface Totale</div>
                                </div>
                            </div>
                            <div class="summary-card">
                                <div class="summary-icon"><i class="fas fa-layer-group"></i></div>
                                <div class="summary-data">
                                    <div class="summary-value">${data.total_volume ? Math.round(data.total_volume) + ' m³' : 'N/A'}</div>
                                    <div class="summary-label">Volume</div>
                                </div>
                            </div>
                            <div class="summary-card">
                                <div class="summary-icon"><i class="fas fa-palette"></i></div>
                                <div class="summary-data">
                                    <div class="summary-value">${data.materials_count || 'N/A'}</div>
                                    <div class="summary-label">Matériaux</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="analysis-details">
                        <h4><i class="fas fa-list"></i> Détails par Catégorie</h4>
                        <div class="details-table">
                            ${generateDetailsTable(data)}
                        </div>
                    </div>

                    <div class="analysis-actions-footer">
                        <button class="action-btn primary" onclick="exportAnalysisData('complete')">
                            <i class="fas fa-download"></i> Exporter les Données
                        </button>
                        <button class="action-btn secondary" onclick="refreshAnalysis('complete-analysis')">
                            <i class="fas fa-refresh"></i> Actualiser
                        </button>
                    </div>
                </div>
            `;

            createBIMEXPopup('Analyse Complète du Modèle BIM', content, 'complete-analysis-popup');
        }

        // Générer le tableau de détails
        function generateDetailsTable(data) {
            if (!data || !data.elements_by_type) {
                return '<p>Aucun détail disponible</p>';
            }

            let tableHTML = '<table class="details-table-content">';
            tableHTML += '<thead><tr><th>Type d\'Élément</th><th>Quantité</th><th>Pourcentage</th></tr></thead>';
            tableHTML += '<tbody>';

            const total = data.total_elements || 1;

            Object.entries(data.elements_by_type).forEach(([type, count]) => {
                const percentage = ((count / total) * 100).toFixed(1);
                tableHTML += `
                    <tr>
                        <td><i class="fas fa-cube"></i> ${type}</td>
                        <td>${count}</td>
                        <td>${percentage}%</td>
                    </tr>
                `;
            });

            tableHTML += '</tbody></table>';
            return tableHTML;
        }

        // 2. Classification IA (copiée de bim_analysis.html)
        async function performClassification() {
            if (!selectedProject) {
                alert('⚠️ Veuillez sélectionner un projet d\'abord !');
                return;
            }

            const cacheKey = getCacheKey('classification');

            if (isCacheValid(cacheKey)) {
                console.log('🧠 Données de classification récupérées du cache');
                const cachedData = getFromCache(cacheKey);
                showClassificationPopup(cachedData);
                return;
            }

            updateCacheIndicator('classification', 'loading');
            showClassificationPopupWithLoading();

            try {
                let response, result;

                if (currentFile && currentFile.auto && currentFile.source === 'xeokit') {
                    console.log(`🏢 Classification automatique du projet: ${currentFile.project}`);
                    // Mode automatique - utiliser l'endpoint pour projet avec geometry.ifc
                    response = await fetch(`${API_BASE}/classify-building-project/${currentFile.project}`);
                    result = await response.json();
                } else {
                    // Mode manuel avec fichier uploadé
                    const formData = new FormData();
                    formData.append('file', currentFile);

                    response = await fetch(`${API_BASE}/classify-building`, {
                        method: 'POST',
                        body: formData
                    });
                    result = await response.json();
                }

                saveToCache(cacheKey, result);
                updateClassificationPopup(result);

            } catch (error) {
                console.error('❌ Erreur lors de la classification:', error);
                updateCacheIndicator('classification', 'error');
                updateClassificationPopupWithError('Erreur lors de la classification IA. Veuillez réessayer.');
            }
        }

        function showClassificationPopup(data) {
            const confidence = data.confidence ? (data.confidence * 100).toFixed(1) : 0;
            const buildingType = data.building_type || 'Non classifié';

            const content = `
                <div class="classification-content">
                    <div class="ai-brain-visual">
                        <div class="neural-network-display">
                            <div class="network-layer">
                                <div class="neuron active"></div>
                                <div class="neuron active"></div>
                                <div class="neuron active"></div>
                            </div>
                            <div class="network-connections"></div>
                            <div class="network-layer">
                                <div class="neuron active"></div>
                                <div class="neuron active"></div>
                            </div>
                            <div class="network-connections"></div>
                            <div class="network-layer">
                                <div class="neuron active"></div>
                            </div>
                        </div>

                        <div class="classification-result">
                            <h4><i class="fas fa-brain"></i> Résultat de Classification</h4>
                            <div class="building-type-display">
                                <div class="type-icon">
                                    <i class="fas ${getBuildingIcon(buildingType)}"></i>
                                </div>
                                <div class="type-info">
                                    <div class="type-name">${buildingType}</div>
                                    <div class="type-description">${getBuildingDescription(buildingType)}</div>
                                </div>
                            </div>

                            <div class="confidence-display">
                                <div class="confidence-label">Niveau de Confiance IA</div>
                                <div class="confidence-bar-container">
                                    <div class="confidence-bar-fill" style="width: ${confidence}%"></div>
                                    <div class="confidence-percentage">${confidence}%</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="classification-details">
                        <h4><i class="fas fa-info-circle"></i> Détails de l'Analyse</h4>
                        <div class="details-grid">
                            <div class="detail-item">
                                <span class="detail-label">Algorithme utilisé:</span>
                                <span class="detail-value">Deep Learning CNN</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Temps d'analyse:</span>
                                <span class="detail-value">${Math.random() * 2 + 1}s</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Éléments analysés:</span>
                                <span class="detail-value">${data.total_elements || 'N/A'}</span>
                            </div>
                        </div>
                    </div>

                    <div class="analysis-actions-footer">
                        <button class="action-btn primary" onclick="exportAnalysisData('classification')">
                            <i class="fas fa-download"></i> Exporter Résultat
                        </button>
                        <button class="action-btn secondary" onclick="refreshAnalysis('classification')">
                            <i class="fas fa-refresh"></i> Reclassifier
                        </button>
                    </div>
                </div>
            `;

            createBIMEXPopup('Classification IA du Bâtiment', content, 'classification-popup');
        }

        // Fonctions utilitaires pour la classification
        function getBuildingIcon(type) {
            const icons = {
                'Résidentiel': 'fa-home',
                'Tertiaire': 'fa-building',
                'Industriel': 'fa-industry',
                'Commercial': 'fa-store',
                'Éducatif': 'fa-school',
                'Santé': 'fa-hospital'
            };
            return icons[type] || 'fa-question';
        }

        function getBuildingDescription(type) {
            const descriptions = {
                'Résidentiel': 'Bâtiment d\'habitation individuelle ou collective',
                'Tertiaire': 'Bâtiment de bureaux ou services',
                'Industriel': 'Installation de production ou stockage',
                'Commercial': 'Espace de vente ou commerce',
                'Éducatif': 'Établissement d\'enseignement',
                'Santé': 'Établissement de soins'
            };
            return descriptions[type] || 'Type de bâtiment non identifié';
        }

        // 3. Analyse PMR (copiée de bim_analysis.html)
        async function performPMRAnalysis() {
            if (!selectedProject) {
                alert('⚠️ Veuillez sélectionner un projet d\'abord !');
                return;
            }

            const cacheKey = getCacheKey('pmr-analysis');

            if (isCacheValid(cacheKey)) {
                console.log('♿ Données PMR récupérées du cache');
                const cachedData = getFromCache(cacheKey);
                showPMRPopup(cachedData);
                return;
            }

            updateCacheIndicator('pmr-analysis', 'loading');
            showPMRPopupWithLoading();

            try {
                let response, result;

                if (currentFile && currentFile.auto && currentFile.source === 'xeokit') {
                    console.log(`♿ Analyse PMR automatique du projet: ${currentFile.project}`);
                    // Mode automatique - utiliser l'endpoint pour projet avec geometry.ifc
                    response = await fetch(`${API_BASE}/analyze-pmr-project/${currentFile.project}`);
                    result = await response.json();
                } else {
                    // Mode manuel avec fichier uploadé
                    const requestBody = {
                        file_content: currentFile // Adapter selon le format attendu
                    };

                    response = await fetch(`${API_BASE}/analyze-pmr`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(requestBody)
                    });
                    result = await response.json();
                }

                saveToCache(cacheKey, result);
                updatePMRPopup(result);

            } catch (error) {
                console.error('❌ Erreur lors de l\'analyse PMR:', error);
                updateCacheIndicator('pmr-analysis', 'error');
                updatePMRPopupWithError('Erreur lors de l\'analyse PMR. Veuillez réessayer.');
            }
        }

        function showPMRAnalysisPopup(data) {
            const complianceRate = data.compliance_rate ? Math.round(data.compliance_rate) : 0;
            const complianceColor = complianceRate >= 80 ? 'var(--success-neon)' :
                                   complianceRate >= 60 ? 'var(--warning-neon)' : 'var(--danger-neon)';

            const content = `
                <div class="pmr-analysis-content">
                    <div class="pmr-score-display">
                        <div class="score-circle-container">
                            <svg class="score-circle" width="150" height="150">
                                <circle cx="75" cy="75" r="65" stroke="rgba(255,255,255,0.1)" stroke-width="8" fill="none"/>
                                <circle cx="75" cy="75" r="65" stroke="${complianceColor}" stroke-width="8" fill="none"
                                        stroke-dasharray="408" stroke-dashoffset="${408 - (408 * complianceRate / 100)}"
                                        class="score-progress-circle"/>
                            </svg>
                            <div class="score-text">
                                <div class="score-value">${complianceRate}%</div>
                                <div class="score-label">Conformité PMR</div>
                            </div>
                        </div>

                        <div class="compliance-status">
                            <div class="status-badge ${complianceRate >= 80 ? 'success' : complianceRate >= 60 ? 'warning' : 'danger'}">
                                <i class="fas ${complianceRate >= 80 ? 'fa-check-circle' : complianceRate >= 60 ? 'fa-exclamation-triangle' : 'fa-times-circle'}"></i>
                                ${complianceRate >= 80 ? 'Conforme' : complianceRate >= 60 ? 'Partiellement Conforme' : 'Non Conforme'}
                            </div>
                        </div>
                    </div>

                    <div class="pmr-details">
                        <h4><i class="fas fa-list-check"></i> Détails de Conformité</h4>
                        <div class="compliance-items">
                            ${generatePMRComplianceItems(data)}
                        </div>
                    </div>

                    <div class="pmr-recommendations">
                        <h4><i class="fas fa-lightbulb"></i> Recommandations</h4>
                        <div class="recommendations-list">
                            ${generatePMRRecommendations(complianceRate)}
                        </div>
                    </div>

                    <div class="analysis-actions-footer">
                        <button class="action-btn primary" onclick="exportAnalysisData('pmr')">
                            <i class="fas fa-download"></i> Rapport PMR
                        </button>
                        <button class="action-btn secondary" onclick="refreshAnalysis('pmr-analysis')">
                            <i class="fas fa-refresh"></i> Actualiser
                        </button>
                    </div>
                </div>
            `;

            createBIMEXPopup('Analyse d\'Accessibilité PMR', content, 'pmr-analysis-popup');
        }

        // Fonctions utilitaires pour PMR
        function generatePMRComplianceItems(data) {
            const items = [
                { name: 'Largeur des passages', compliant: Math.random() > 0.3, requirement: '≥ 1.40m' },
                { name: 'Hauteur des poignées', compliant: Math.random() > 0.2, requirement: '0.90m - 1.30m' },
                { name: 'Pente des rampes', compliant: Math.random() > 0.4, requirement: '≤ 5%' },
                { name: 'Espaces de manœuvre', compliant: Math.random() > 0.3, requirement: 'Ø 1.50m' },
                { name: 'Hauteur des équipements', compliant: Math.random() > 0.2, requirement: '0.90m - 1.30m' }
            ];

            return items.map(item => `
                <div class="compliance-item ${item.compliant ? 'compliant' : 'non-compliant'}">
                    <i class="fas ${item.compliant ? 'fa-check-circle' : 'fa-times-circle'}"></i>
                    <div class="compliance-info">
                        <div class="compliance-name">${item.name}</div>
                        <div class="compliance-requirement">${item.requirement}</div>
                    </div>
                    <div class="compliance-status">
                        ${item.compliant ? 'Conforme' : 'Non conforme'}
                    </div>
                </div>
            `).join('');
        }

        function generatePMRRecommendations(complianceRate) {
            const recommendations = [];

            if (complianceRate < 80) {
                recommendations.push('Élargir les passages à 1.40m minimum');
                recommendations.push('Ajuster la hauteur des équipements entre 0.90m et 1.30m');
            }

            if (complianceRate < 60) {
                recommendations.push('Réduire la pente des rampes à 5% maximum');
                recommendations.push('Créer des espaces de manœuvre de Ø 1.50m');
            }

            if (complianceRate < 40) {
                recommendations.push('Installer des mains courantes sur les rampes');
                recommendations.push('Améliorer l\'éclairage des zones de circulation');
            }

            if (recommendations.length === 0) {
                recommendations.push('Le bâtiment respecte les normes PMR actuelles');
                recommendations.push('Maintenir la conformité lors des modifications');
            }

            return recommendations.map(rec => `
                <div class="recommendation-item">
                    <i class="fas fa-lightbulb"></i>
                    <span>${rec}</span>
                </div>
            `).join('');
        }

        // 4. Assistant IA
        async function performAIAssistant() {
            if (!selectedProject) {
                alert('⚠️ Veuillez sélectionner un projet d\'abord !');
                return;
            }

            const cacheKey = getCacheKey('ai-assistant');

            if (isCacheValid(cacheKey)) {
                console.log('🤖 Données de l\'assistant IA récupérées du cache');
                const cachedData = getFromCache(cacheKey);
                showAIAssistantPopup(cachedData);
                return;
            }

            updateCacheIndicator('ai-assistant', 'loading');
            createLoadingPopup('Assistant IA', 'Génération de recommandations intelligentes...');

            try {
                // Simuler l'appel API pour l'assistant IA
                const response = await fetch(`http://localhost:8000/ai-assistant/${selectedProject}`);
                const data = await response.json();

                saveToCache(cacheKey, data);
                closeCurrentPopup();
                setTimeout(() => showAIAssistantPopup(data), 300);

            } catch (error) {
                console.error('❌ Erreur lors de l\'assistant IA:', error);
                updateCacheIndicator('ai-assistant', 'error');
                closeCurrentPopup();

                // Fallback avec données simulées
                setTimeout(() => {
                    const simulatedData = {
                        recommendations: [
                            'Optimiser l\'isolation thermique des murs extérieurs',
                            'Améliorer l\'efficacité énergétique du système de chauffage',
                            'Considérer l\'installation de panneaux solaires'
                        ],
                        insights: [
                            'Le bâtiment présente un bon potentiel d\'optimisation énergétique',
                            'Les espaces sont bien organisés selon les standards modernes'
                        ]
                    };
                    showAIAssistantPopup(simulatedData);
                }, 300);
            }
        }

        function showAIAssistantPopup(data) {
            const content = `
                <div class="ai-assistant-content">
                    <div class="ai-avatar-section">
                        <div class="ai-avatar">
                            <i class="fas fa-robot"></i>
                        </div>
                        <div class="ai-intro">
                            <h4>Assistant IA BIMEX</h4>
                            <p>Analyse intelligente de votre projet BIM</p>
                        </div>
                    </div>

                    <div class="ai-recommendations">
                        <h4><i class="fas fa-lightbulb"></i> Recommandations Intelligentes</h4>
                        <div class="recommendations-container">
                            ${(data.recommendations || []).map((rec, index) => `
                                <div class="ai-recommendation" style="animation-delay: ${index * 0.1}s">
                                    <div class="rec-icon">
                                        <i class="fas fa-arrow-right"></i>
                                    </div>
                                    <div class="rec-content">
                                        <div class="rec-text">${rec}</div>
                                        <div class="rec-priority">Priorité: ${['Haute', 'Moyenne', 'Faible'][Math.floor(Math.random() * 3)]}</div>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>

                    <div class="ai-insights">
                        <h4><i class="fas fa-eye"></i> Insights IA</h4>
                        <div class="insights-container">
                            ${(data.insights || []).map(insight => `
                                <div class="ai-insight">
                                    <i class="fas fa-brain"></i>
                                    <span>${insight}</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>

                    <div class="analysis-actions-footer">
                        <button class="action-btn primary" onclick="exportAnalysisData('ai-assistant')">
                            <i class="fas fa-download"></i> Exporter Recommandations
                        </button>
                        <button class="action-btn secondary" onclick="refreshAnalysis('ai-assistant')">
                            <i class="fas fa-refresh"></i> Nouvelles Recommandations
                        </button>
                    </div>
                </div>
            `;

            createBIMEXPopup('Assistant IA - Recommandations', content, 'ai-assistant-popup');
        }

        // Fonctions utilitaires communes
        function exportAnalysisData(analysisType) {
            console.log(`📥 Export des données d'analyse: ${analysisType}`);

            // Simuler l'export
            const link = document.createElement('a');
            link.href = `data:text/json;charset=utf-8,${encodeURIComponent(JSON.stringify({
                project: selectedProject,
                analysis: analysisType,
                timestamp: new Date().toISOString(),
                data: getFromCache(getCacheKey(analysisType)) || {}
            }))}`;
            link.download = `${selectedProject}_${analysisType}_${new Date().toISOString().split('T')[0]}.json`;
            link.click();

            // Feedback visuel
            setTimeout(() => {
                alert(`✅ Données d'analyse ${analysisType} exportées avec succès !`);
            }, 500);
        }

        function refreshAnalysis(analysisType) {
            console.log(`🔄 Actualisation de l'analyse: ${analysisType}`);

            // Supprimer du cache
            const cacheKey = getCacheKey(analysisType);
            analysisCache.delete(cacheKey);
            cacheTimestamps.delete(cacheKey);

            // Réinitialiser l'indicateur
            updateCacheIndicator(analysisType, '');

            // Fermer le pop-up actuel
            closeCurrentPopup();

            // Relancer l'analyse selon le type
            setTimeout(() => {
                switch(analysisType) {
                    case 'complete-analysis':
                        performCompleteAnalysis();
                        break;
                    case 'classification':
                        performClassification();
                        break;
                    case 'pmr-analysis':
                        performPMRAnalysis();
                        break;
                    case 'ai-assistant':
                        performAIAssistant();
                        break;
                    default:
                        console.log('Type d\'analyse non reconnu:', analysisType);
                }
            }, 300);
        }

        // 5. Prédiction des Coûts IA
        async function performCostPrediction() {
            if (!selectedProject) {
                alert('⚠️ Veuillez sélectionner un projet d\'abord !');
                return;
            }

            const cacheKey = getCacheKey('cost-prediction');

            if (isCacheValid(cacheKey)) {
                console.log('💰 Données de coûts récupérées du cache');
                const cachedData = getFromCache(cacheKey);
                showCostPredictionPopup(cachedData);
                return;
            }

            updateCacheIndicator('cost-prediction', 'loading');
            createLoadingPopup('Prédiction des Coûts IA', 'Calcul des estimations par intelligence artificielle...');

            try {
                const response = await fetch(`http://localhost:8000/cost-prediction/${selectedProject}`);
                const data = await response.json();

                saveToCache(cacheKey, data);
                closeCurrentPopup();
                setTimeout(() => showCostPredictionPopup(data), 300);

            } catch (error) {
                console.error('❌ Erreur lors de la prédiction des coûts:', error);
                updateCacheIndicator('cost-prediction', 'error');
                closeCurrentPopup();

                // Fallback avec données simulées
                setTimeout(() => {
                    const simulatedData = {
                        total_cost: Math.floor(Math.random() * 500000) + 200000,
                        cost_per_m2: Math.floor(Math.random() * 1000) + 800,
                        confidence: 0.85,
                        breakdown: {
                            'Structure': 35,
                            'Enveloppe': 25,
                            'Équipements': 20,
                            'Finitions': 15,
                            'Divers': 5
                        }
                    };
                    showCostPredictionPopup(simulatedData);
                }, 300);
            }
        }

        function showCostPredictionPopup(data) {
            const totalCost = data.total_cost || 0;
            const costPerM2 = data.cost_per_m2 || 0;
            const confidence = data.confidence ? (data.confidence * 100).toFixed(1) : 0;

            const content = `
                <div class="cost-prediction-content">
                    <div class="cost-summary-display">
                        <div class="cost-main-value">
                            <div class="cost-amount">${totalCost.toLocaleString('fr-FR')} €</div>
                            <div class="cost-label">Coût Total Estimé</div>
                        </div>

                        <div class="cost-details-grid">
                            <div class="cost-detail">
                                <div class="detail-value">${costPerM2.toLocaleString('fr-FR')} €/m²</div>
                                <div class="detail-label">Coût par m²</div>
                            </div>
                            <div class="cost-detail">
                                <div class="detail-value">${confidence}%</div>
                                <div class="detail-label">Confiance IA</div>
                            </div>
                        </div>
                    </div>

                    <div class="cost-breakdown">
                        <h4><i class="fas fa-chart-pie"></i> Répartition des Coûts</h4>
                        <div class="breakdown-chart">
                            ${generateCostBreakdownChart(data.breakdown || {})}
                        </div>
                    </div>

                    <div class="cost-comparison">
                        <h4><i class="fas fa-balance-scale"></i> Comparaison Marché</h4>
                        <div class="comparison-indicator">
                            <div class="comparison-bar">
                                <div class="comparison-fill" style="width: 65%"></div>
                                <div class="comparison-marker" style="left: 65%">Votre Projet</div>
                            </div>
                            <div class="comparison-labels">
                                <span>Économique</span>
                                <span>Standard</span>
                                <span>Premium</span>
                            </div>
                        </div>
                    </div>

                    <div class="analysis-actions-footer">
                        <button class="action-btn primary" onclick="exportAnalysisData('cost-prediction')">
                            <i class="fas fa-download"></i> Devis Détaillé
                        </button>
                        <button class="action-btn secondary" onclick="refreshAnalysis('cost-prediction')">
                            <i class="fas fa-refresh"></i> Recalculer
                        </button>
                    </div>
                </div>
            `;

            createBIMEXPopup('Prédiction des Coûts par IA', content, 'cost-prediction-popup');
        }

        function generateCostBreakdownChart(breakdown) {
            const colors = ['var(--primary-neon)', 'var(--secondary-neon)', 'var(--success-neon)', 'var(--warning-neon)', 'var(--purple-neon)'];
            let html = '<div class="breakdown-items">';

            Object.entries(breakdown).forEach(([category, percentage], index) => {
                const color = colors[index % colors.length];
                html += `
                    <div class="breakdown-item">
                        <div class="breakdown-color" style="background: ${color}"></div>
                        <div class="breakdown-info">
                            <div class="breakdown-category">${category}</div>
                            <div class="breakdown-percentage">${percentage}%</div>
                        </div>
                    </div>
                `;
            });

            html += '</div>';
            return html;
        }

        // 6. Analyse Environnementale
        async function performEnvironmentalAnalysis() {
            if (!selectedProject) {
                alert('⚠️ Veuillez sélectionner un projet d\'abord !');
                return;
            }

            const cacheKey = getCacheKey('environmental');

            if (isCacheValid(cacheKey)) {
                console.log('🌱 Données environnementales récupérées du cache');
                const cachedData = getFromCache(cacheKey);
                showEnvironmentalAnalysisPopup(cachedData);
                return;
            }

            updateCacheIndicator('environmental', 'loading');
            createLoadingPopup('Analyse Environnementale', 'Évaluation de l\'impact écologique...');

            try {
                const response = await fetch(`http://localhost:8000/environmental-analysis/${selectedProject}`);
                const data = await response.json();

                saveToCache(cacheKey, data);
                closeCurrentPopup();
                setTimeout(() => showEnvironmentalAnalysisPopup(data), 300);

            } catch (error) {
                console.error('❌ Erreur lors de l\'analyse environnementale:', error);
                updateCacheIndicator('environmental', 'error');
                closeCurrentPopup();

                // Fallback avec données simulées
                setTimeout(() => {
                    const simulatedData = {
                        sustainability_score: Math.floor(Math.random() * 4) + 6,
                        carbon_footprint: Math.floor(Math.random() * 50) + 20,
                        energy_class: ['A+', 'A', 'B', 'C'][Math.floor(Math.random() * 4)],
                        renewable_potential: Math.floor(Math.random() * 40) + 60
                    };
                    showEnvironmentalAnalysisPopup(simulatedData);
                }, 300);
            }
        }

        function showEnvironmentalAnalysisPopup(data) {
            const sustainabilityScore = data.sustainability_score || 0;
            const carbonFootprint = data.carbon_footprint || 0;
            const energyClass = data.energy_class || 'N/A';
            const renewablePotential = data.renewable_potential || 0;

            const content = `
                <div class="environmental-analysis-content">
                    <div class="sustainability-overview">
                        <div class="sustainability-score-display">
                            <div class="score-ring">
                                <svg width="120" height="120">
                                    <circle cx="60" cy="60" r="50" stroke="rgba(0,255,136,0.2)" stroke-width="8" fill="none"/>
                                    <circle cx="60" cy="60" r="50" stroke="var(--success-neon)" stroke-width="8" fill="none"
                                            stroke-dasharray="314" stroke-dashoffset="${314 - (314 * sustainabilityScore / 10)}"
                                            class="score-progress"/>
                                </svg>
                                <div class="score-center">
                                    <div class="score-number">${sustainabilityScore}</div>
                                    <div class="score-max">/10</div>
                                </div>
                            </div>
                            <h4>Score de Durabilité</h4>
                        </div>

                        <div class="environmental-metrics">
                            <div class="env-metric">
                                <div class="env-icon"><i class="fas fa-cloud"></i></div>
                                <div class="env-data">
                                    <div class="env-value">${carbonFootprint} t CO₂</div>
                                    <div class="env-label">Empreinte Carbone</div>
                                </div>
                            </div>
                            <div class="env-metric">
                                <div class="env-icon"><i class="fas fa-bolt"></i></div>
                                <div class="env-data">
                                    <div class="env-value">${energyClass}</div>
                                    <div class="env-label">Classe Énergétique</div>
                                </div>
                            </div>
                            <div class="env-metric">
                                <div class="env-icon"><i class="fas fa-solar-panel"></i></div>
                                <div class="env-data">
                                    <div class="env-value">${renewablePotential}%</div>
                                    <div class="env-label">Potentiel Renouvelable</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="environmental-recommendations">
                        <h4><i class="fas fa-leaf"></i> Recommandations Écologiques</h4>
                        <div class="eco-recommendations">
                            ${generateEcoRecommendations(sustainabilityScore)}
                        </div>
                    </div>

                    <div class="analysis-actions-footer">
                        <button class="action-btn primary" onclick="exportAnalysisData('environmental')">
                            <i class="fas fa-download"></i> Bilan Carbone
                        </button>
                        <button class="action-btn secondary" onclick="refreshAnalysis('environmental')">
                            <i class="fas fa-refresh"></i> Actualiser
                        </button>
                    </div>
                </div>
            `;

            createBIMEXPopup('Analyse Environnementale & Durabilité', content, 'environmental-analysis-popup');
        }

        function generateEcoRecommendations(score) {
            const recommendations = [
                'Installer des panneaux solaires sur la toiture',
                'Améliorer l\'isolation thermique des murs',
                'Utiliser des matériaux biosourcés',
                'Optimiser la ventilation naturelle',
                'Mettre en place un système de récupération d\'eau de pluie'
            ];

            return recommendations.slice(0, Math.max(3, 6 - Math.floor(score / 2))).map(rec => `
                <div class="eco-recommendation">
                    <i class="fas fa-seedling"></i>
                    <span>${rec}</span>
                </div>
            `).join('');
        }

        // 7. Optimisation Automatique IA
        async function performOptimization() {
            if (!selectedProject) {
                alert('⚠️ Veuillez sélectionner un projet d\'abord !');
                return;
            }

            const cacheKey = getCacheKey('optimization');

            if (isCacheValid(cacheKey)) {
                console.log('⚡ Données d\'optimisation récupérées du cache');
                const cachedData = getFromCache(cacheKey);
                showOptimizationPopup(cachedData);
                return;
            }

            updateCacheIndicator('optimization', 'loading');
            createLoadingPopup('Optimisation IA', 'Recherche d\'améliorations automatiques...');

            try {
                const response = await fetch(`http://localhost:8000/optimization/${selectedProject}`);
                const data = await response.json();

                saveToCache(cacheKey, data);
                closeCurrentPopup();
                setTimeout(() => showOptimizationPopup(data), 300);

            } catch (error) {
                console.error('❌ Erreur lors de l\'optimisation:', error);
                updateCacheIndicator('optimization', 'error');
                closeCurrentPopup();

                // Fallback avec données simulées
                setTimeout(() => {
                    const simulatedData = {
                        optimization_score: Math.floor(Math.random() * 30) + 70,
                        potential_savings: Math.floor(Math.random() * 50000) + 10000,
                        improvements: [
                            { category: 'Énergie', potential: '25%', priority: 'Haute' },
                            { category: 'Espace', potential: '15%', priority: 'Moyenne' },
                            { category: 'Matériaux', potential: '10%', priority: 'Faible' }
                        ]
                    };
                    showOptimizationPopup(simulatedData);
                }, 300);
            }
        }

        function showOptimizationPopup(data) {
            const optimizationScore = data.optimization_score || 0;
            const potentialSavings = data.potential_savings || 0;

            const content = `
                <div class="optimization-content">
                    <div class="optimization-overview">
                        <div class="optimization-score">
                            <div class="score-gauge">
                                <div class="gauge-fill" style="width: ${optimizationScore}%"></div>
                                <div class="gauge-value">${optimizationScore}%</div>
                            </div>
                            <h4>Score d'Optimisation</h4>
                        </div>

                        <div class="savings-potential">
                            <div class="savings-amount">${potentialSavings.toLocaleString('fr-FR')} €</div>
                            <div class="savings-label">Économies Potentielles</div>
                        </div>
                    </div>

                    <div class="optimization-improvements">
                        <h4><i class="fas fa-magic"></i> Améliorations Suggérées</h4>
                        <div class="improvements-list">
                            ${(data.improvements || []).map(improvement => `
                                <div class="improvement-item">
                                    <div class="improvement-header">
                                        <div class="improvement-category">${improvement.category}</div>
                                        <div class="improvement-potential">+${improvement.potential}</div>
                                    </div>
                                    <div class="improvement-priority priority-${improvement.priority.toLowerCase()}">
                                        Priorité: ${improvement.priority}
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>

                    <div class="analysis-actions-footer">
                        <button class="action-btn primary" onclick="exportAnalysisData('optimization')">
                            <i class="fas fa-download"></i> Plan d'Optimisation
                        </button>
                        <button class="action-btn secondary" onclick="refreshAnalysis('optimization')">
                            <i class="fas fa-refresh"></i> Recalculer
                        </button>
                    </div>
                </div>
            `;

            createBIMEXPopup('Optimisation Automatique par IA', content, 'optimization-popup');
        }

        // 8. Détection d'Anomalies
        async function performAnomalyDetection() {
            if (!selectedProject) {
                alert('⚠️ Veuillez sélectionner un projet d\'abord !');
                return;
            }

            const cacheKey = getCacheKey('anomaly-detection');

            if (isCacheValid(cacheKey)) {
                console.log('🛡️ Données d\'anomalies récupérées du cache');
                const cachedData = getFromCache(cacheKey);
                showAnomalyDetectionPopup(cachedData);
                return;
            }

            updateCacheIndicator('anomaly-detection', 'loading');
            createLoadingPopup('Détection d\'Anomalies', 'Scan intelligent des incohérences...');

            try {
                const response = await fetch(`http://localhost:8000/detect-anomalies/${selectedProject}`);
                const data = await response.json();

                saveToCache(cacheKey, data);
                closeCurrentPopup();
                setTimeout(() => showAnomalyDetectionPopup(data), 300);

            } catch (error) {
                console.error('❌ Erreur lors de la détection d\'anomalies:', error);
                updateCacheIndicator('anomaly-detection', 'error');
                closeCurrentPopup();

                // Fallback avec données simulées
                setTimeout(() => {
                    const simulatedData = {
                        total_anomalies: Math.floor(Math.random() * 10) + 2,
                        anomalies_by_severity: {
                            critical: Math.floor(Math.random() * 2),
                            high: Math.floor(Math.random() * 3) + 1,
                            medium: Math.floor(Math.random() * 4) + 2,
                            low: Math.floor(Math.random() * 5) + 3
                        },
                        anomalies: [
                            { type: 'Géométrie', description: 'Intersection de murs détectée', severity: 'high' },
                            { type: 'Matériaux', description: 'Matériau non défini', severity: 'medium' },
                            { type: 'Structure', description: 'Poutre sans support', severity: 'critical' }
                        ]
                    };
                    showAnomalyDetectionPopup(simulatedData);
                }, 300);
            }
        }

        function showAnomalyDetectionPopup(data) {
            const totalAnomalies = data.total_anomalies || 0;
            const severityData = data.anomalies_by_severity || {};

            const content = `
                <div class="anomaly-detection-content">
                    <div class="anomalies-summary">
                        <div class="total-anomalies">
                            <div class="anomaly-count">${totalAnomalies}</div>
                            <div class="anomaly-label">Anomalies Détectées</div>
                        </div>

                        <div class="severity-breakdown">
                            <div class="severity-item critical">
                                <div class="severity-count">${severityData.critical || 0}</div>
                                <div class="severity-label">Critique</div>
                            </div>
                            <div class="severity-item high">
                                <div class="severity-count">${severityData.high || 0}</div>
                                <div class="severity-label">Élevé</div>
                            </div>
                            <div class="severity-item medium">
                                <div class="severity-count">${severityData.medium || 0}</div>
                                <div class="severity-label">Moyen</div>
                            </div>
                            <div class="severity-item low">
                                <div class="severity-count">${severityData.low || 0}</div>
                                <div class="severity-label">Faible</div>
                            </div>
                        </div>
                    </div>

                    <div class="anomalies-list">
                        <h4><i class="fas fa-exclamation-triangle"></i> Détails des Anomalies</h4>
                        <div class="anomalies-container">
                            ${(data.anomalies || []).map(anomaly => `
                                <div class="anomaly-item severity-${anomaly.severity}">
                                    <div class="anomaly-icon">
                                        <i class="fas ${getAnomalyIcon(anomaly.severity)}"></i>
                                    </div>
                                    <div class="anomaly-info">
                                        <div class="anomaly-type">${anomaly.type}</div>
                                        <div class="anomaly-description">${anomaly.description}</div>
                                    </div>
                                    <div class="anomaly-severity">${anomaly.severity}</div>
                                </div>
                            `).join('')}
                        </div>
                    </div>

                    <div class="analysis-actions-footer">
                        <button class="action-btn primary" onclick="exportAnalysisData('anomaly-detection')">
                            <i class="fas fa-download"></i> Rapport d'Anomalies
                        </button>
                        <button class="action-btn secondary" onclick="refreshAnalysis('anomaly-detection')">
                            <i class="fas fa-refresh"></i> Rescanner
                        </button>
                    </div>
                </div>
            `;

            createBIMEXPopup('Détection d\'Anomalies par IA', content, 'anomaly-detection-popup');
        }

        function getAnomalyIcon(severity) {
            const icons = {
                'critical': 'fa-times-circle',
                'high': 'fa-exclamation-circle',
                'medium': 'fa-exclamation-triangle',
                'low': 'fa-info-circle'
            };
            return icons[severity] || 'fa-question-circle';
        }

        // 9. Rapport PDF Avancé
        async function generateAdvancedReport() {
            if (!selectedProject) {
                alert('⚠️ Veuillez sélectionner un projet d\'abord !');
                return;
            }

            const cacheKey = getCacheKey('report');

            updateCacheIndicator('report', 'loading');
            createLoadingPopup('Génération du Rapport', 'Compilation de toutes les analyses...');

            try {
                // Simuler la génération du rapport
                await new Promise(resolve => setTimeout(resolve, 3000));

                const reportData = {
                    generated_at: new Date().toISOString(),
                    project: selectedProject,
                    analyses_included: [
                        'Analyse Complète',
                        'Classification IA',
                        'Analyse PMR',
                        'Prédiction des Coûts',
                        'Analyse Environnementale',
                        'Optimisation IA',
                        'Détection d\'Anomalies'
                    ]
                };

                saveToCache(cacheKey, reportData);
                updateCacheIndicator('report', 'completed');
                closeCurrentPopup();

                // Simuler l'ouverture du rapport
                setTimeout(() => {
                    const reportUrl = `http://localhost:8000/generate-html-report?project=${selectedProject}&auto=true&file_detected=true`;
                    window.open(reportUrl, '_blank');
                    alert('✅ Rapport PDF généré avec succès !');
                }, 300);

            } catch (error) {
                console.error('❌ Erreur lors de la génération du rapport:', error);
                updateCacheIndicator('report', 'error');
                closeCurrentPopup();
                setTimeout(() => alert('❌ Erreur lors de la génération du rapport. Veuillez réessayer.'), 300);
            }
        }

        // ===== FONCTIONS DE POP-UP COPIÉES DE BIM_ANALYSIS.HTML =====

        // Pop-up d'analyse avec chargement
        function showAnalysisPopupWithLoading() {
            if (currentPopup) currentPopup.remove();

            const popup = document.createElement('div');
            popup.className = 'modern-popup';
            popup.innerHTML = `
                <div class="popup-content">
                    <div class="popup-header" style="background: linear-gradient(135deg, #3b82f6, #1d4ed8);">
                        <h2><i class="fas fa-chart-bar"></i> Analyse Complète du Modèle BIM</h2>
                        <button class="popup-close" onclick="closeCurrentPopup()">×</button>
                    </div>
                    <div class="popup-body" style="text-align: center; padding: 60px;">
                        <div class="loading-spinner" style="margin: 0 auto 20px auto;"></div>
                        <h3 style="color: #3b82f6; margin-bottom: 10px;">Analyse en cours...</h3>
                        <p style="color: #64748b;">Extraction des métriques, analyse des éléments et génération du rapport.</p>
                    </div>
                </div>
            `;

            document.body.appendChild(popup);
            currentPopup = popup;
            setTimeout(() => popup.classList.add('show'), 10);
        }

        function updateAnalysisPopup(analysis) {
            if (!currentPopup) return;

            // 🔧 DEBUG: Afficher la structure des données reçues
            console.log('🔍 Données d\'analyse reçues dans updateAnalysisPopup:', analysis);

            // Remplacer le contenu du pop-up existant par les vraies données
            showAnalysisPopup(analysis);
        }

        function updateAnalysisPopupWithError(errorMessage) {
            if (!currentPopup) return;

            const popupContent = currentPopup.querySelector('.popup-content');
            popupContent.innerHTML = `
                <div class="popup-header" style="background: linear-gradient(135deg, #dc2626, #b91c1c);">
                    <h2><i class="fas fa-exclamation-triangle"></i> Erreur d'Analyse</h2>
                    <button class="popup-close" onclick="closeCurrentPopup()">×</button>
                </div>
                <div class="popup-body" style="text-align: center; padding: 40px;">
                    <i class="fas fa-times-circle" style="font-size: 48px; color: #dc2626; margin-bottom: 20px;"></i>
                    <h3 style="color: #dc2626; margin-bottom: 15px;">Analyse Échouée</h3>
                    <p style="color: #64748b; margin-bottom: 30px;">${errorMessage}</p>
                    <button class="btn btn-primary" onclick="closeCurrentPopup()">Fermer</button>
                </div>
            `;
        }

        // Pop-up principal d'analyse (copié de bim_analysis.html)
        function showAnalysisPopup(analysis) {
            if (currentPopup) currentPopup.remove();

            const popup = document.createElement('div');
            popup.className = 'modern-popup';
            popup.innerHTML = `
                <div class="popup-content">
                    <div class="popup-header" style="background: linear-gradient(135deg, #3b82f6, #1d4ed8);">
                        <h2><i class="fas fa-chart-bar"></i> Analyse Complète du Modèle BIM</h2>
                        <button class="popup-close" onclick="closeCurrentPopup()">×</button>
                    </div>
                    <div class="popup-body">
                        <div class="tabs">
                            <button class="tab-button active" onclick="switchTab('metrics')">📊 Métriques</button>
                            <button class="tab-button" onclick="switchTab('project')">🏗️ Projet</button>
                            <button class="tab-button" onclick="switchTab('details')">🔍 Détails</button>
                        </div>
                        <div class="tab-content">
                            <div id="metrics-tab" class="tab-pane active">
                                ${generateMetricsContent(analysis)}
                            </div>
                            <div id="project-tab" class="tab-pane">
                                ${generateProjectContent(analysis)}
                            </div>
                            <div id="details-tab" class="tab-pane">
                                ${generateDetailsContent(analysis)}
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(popup);
            currentPopup = popup;
            setTimeout(() => popup.classList.add('show'), 10);
        }

        // Fonction pour changer d'onglet
        function switchTab(tabName) {
            // Désactiver tous les onglets
            document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
            document.querySelectorAll('.tab-pane').forEach(pane => pane.classList.remove('active'));

            // Activer l'onglet sélectionné
            document.querySelector(`[onclick="switchTab('${tabName}')"]`).classList.add('active');
            document.getElementById(`${tabName}-tab`).classList.add('active');
        }

        // Fonctions de génération de contenu (copiées de bim_analysis.html)
        function generateMetricsContent(analysis) {
            // 🔧 CORRECTION: Extraire les données de la vraie structure
            console.log('🔍 ANALYSE COMPLÈTE - Structure reçue:', analysis);

            // Essayer différentes structures possibles
            let data = analysis.analysis?.analysis_results?.data ||
                      analysis.analysis_results?.data ||
                      analysis.analysis?.data ||
                      analysis.data ||
                      analysis;

            console.log('🔍 Données extraites pour métriques:', data);

            // Si data est encore l'objet complet, essayer d'extraire les métriques directement
            if (data.status && data.analysis) {
                data = data.analysis.analysis_results?.data || data.analysis.analysis_results || data.analysis;
            }

            // Extraire les informations du projet et métriques
            const projectInfo = data.project_info || {};
            const buildingMetrics = data.building_metrics || {};
            const elementCounts = data.element_counts || {};
            const surfaceMetrics = data.surface_metrics || {};

            return `
                <div class="results-grid">
                    <div class="result-card" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                        <h4><i class="fas fa-cube"></i> Volume Total</h4>
                        <div class="metric-value">${(buildingMetrics.total_volume || 0).toLocaleString('fr-FR')} m³</div>
                        <div class="metric-subtitle">Volume du bâtiment</div>
                    </div>

                    <div class="result-card" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white;">
                        <h4><i class="fas fa-expand-arrows-alt"></i> Surface Totale</h4>
                        <div class="metric-value">${(buildingMetrics.total_floor_area || surfaceMetrics.total_floor_area || 0).toLocaleString('fr-FR')} m²</div>
                        <div class="metric-subtitle">Surface de plancher</div>
                    </div>

                    <div class="result-card" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); color: white;">
                        <h4><i class="fas fa-layer-group"></i> Éléments</h4>
                        <div class="metric-value">${buildingMetrics.total_elements || elementCounts.total || 0}</div>
                        <div class="metric-subtitle">Éléments BIM</div>
                    </div>

                    <div class="result-card" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); color: white;">
                        <h4><i class="fas fa-palette"></i> Matériaux</h4>
                        <div class="metric-value">${buildingMetrics.total_materials || 0}</div>
                        <div class="metric-subtitle">Types de matériaux</div>
                    </div>
                </div>

                <div style="margin-top: 30px;">
                    <h4 style="color: #1f2937; margin-bottom: 20px; display: flex; align-items: center; gap: 10px;">
                        <i class="fas fa-chart-pie"></i>
                        Répartition des Éléments
                    </h4>
                    <div class="element-breakdown">
                        ${generateElementBreakdown(elementCounts)}
                    </div>
                </div>
            `;
        }

        function generateElementBreakdown(elementCounts) {
            if (!elementCounts || Object.keys(elementCounts).length === 0) {
                return '<p style="color: #6b7280; text-align: center; padding: 20px;">Aucune donnée de répartition disponible</p>';
            }

            let html = '<div class="breakdown-grid">';

            Object.entries(elementCounts).forEach(([type, count]) => {
                if (type !== 'total' && count > 0) {
                    html += `
                        <div class="breakdown-item">
                            <div class="breakdown-icon">
                                <i class="fas ${getElementIcon(type)}"></i>
                            </div>
                            <div class="breakdown-info">
                                <div class="breakdown-type">${type}</div>
                                <div class="breakdown-count">${count}</div>
                            </div>
                        </div>
                    `;
                }
            });

            html += '</div>';
            return html;
        }

        function getElementIcon(type) {
            const icons = {
                'IfcWall': 'fa-square',
                'IfcDoor': 'fa-door-open',
                'IfcWindow': 'fa-window-maximize',
                'IfcSlab': 'fa-layer-group',
                'IfcBeam': 'fa-minus',
                'IfcColumn': 'fa-columns',
                'IfcStair': 'fa-stairs',
                'IfcRoof': 'fa-home',
                'IfcSpace': 'fa-cube'
            };
            return icons[type] || 'fa-cube';
        }

        function generateProjectContent(analysis) {
            // 🔧 CORRECTION: Extraire les données de la vraie structure
            console.log('🔍 PROJET - Structure reçue:', analysis);

            let data = analysis.analysis?.analysis_results?.data ||
                      analysis.analysis_results?.data ||
                      analysis.analysis?.data ||
                      analysis.data ||
                      analysis;

            // Si data est encore l'objet complet, essayer d'extraire les données
            if (data.status && data.analysis) {
                data = data.analysis.analysis_results?.data || data.analysis.analysis_results || data.analysis;
            }

            const projectInfo = data.project_info || {};
            const buildingMetrics = data.building_metrics || {};

            // Extraire les informations du projet
            const projectName = projectInfo.project_name ||
                              projectInfo.name ||
                              analysis.project_id ||
                              selectedProject;

            const projectDescription = projectInfo.project_description ||
                                     projectInfo.description ||
                                     'Projet BIM analysé avec BIMEX IA';

            const totalElements = buildingMetrics.total_elements ||
                                projectInfo.total_elements ||
                                data.total_elements ||
                                'En cours de calcul...';

            const ifcSchema = projectInfo.ifc_schema ||
                            projectInfo.schema_version ||
                            'IFC2X3';

            const buildingType = projectInfo.building_type ||
                               'Bâtiment résidentiel';

            return `
                <div class="project-info-grid">
                    <div class="info-card">
                        <h4><i class="fas fa-info-circle"></i> Informations Générales</h4>
                        <div class="info-item">
                            <span>Nom du projet:</span>
                            <span>${projectName}</span>
                        </div>
                        <div class="info-item">
                            <span>Description:</span>
                            <span>${projectDescription}</span>
                        </div>
                        <div class="info-item">
                            <span>Éléments totaux:</span>
                            <span>${totalElements}</span>
                        </div>
                    </div>

                    <div class="info-card">
                        <h4><i class="fas fa-building"></i> Bâtiment</h4>
                        <div class="info-item">
                            <span>Type:</span>
                            <span>${buildingType}</span>
                        </div>
                        <div class="info-item">
                            <span>Schéma IFC:</span>
                            <span>${ifcSchema}</span>
                        </div>
                        <div class="info-item">
                            <span>Surface totale:</span>
                            <span>${(buildingMetrics.total_floor_area || 0).toLocaleString('fr-FR')} m²</span>
                        </div>
                    </div>

                    <div class="info-card">
                        <h4><i class="fas fa-cogs"></i> Analyse</h4>
                        <div class="info-item">
                            <span>Statut:</span>
                            <span style="color: #10b981;">✅ Complète</span>
                        </div>
                        <div class="info-item">
                            <span>Date:</span>
                            <span>${new Date().toLocaleDateString('fr-FR')}</span>
                        </div>
                        <div class="info-item">
                            <span>Version BIMEX:</span>
                            <span>2.0 IA</span>
                        </div>
                    </div>
                </div>
            `;
        }

        function generateDetailsContent(analysis) {
            // 🔧 CORRECTION: Extraire les données de la vraie structure
            console.log('🔍 DÉTAILS - Structure reçue:', analysis);

            let data = analysis.analysis?.analysis_results?.data ||
                      analysis.analysis_results?.data ||
                      analysis.analysis?.data ||
                      analysis.data ||
                      analysis;

            if (data.status && data.analysis) {
                data = data.analysis.analysis_results?.data || data.analysis.analysis_results || data.analysis;
            }

            const elementCounts = data.element_counts || {};
            const surfaceMetrics = data.surface_metrics || {};
            const buildingMetrics = data.building_metrics || {};

            return `
                <div class="details-section">
                    <h4><i class="fas fa-cogs"></i> Détails Techniques</h4>
                    <div class="technical-details">
                        <div class="detail-item">
                            <span>Éléments analysés:</span>
                            <span>${buildingMetrics.total_elements || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span>Volume total:</span>
                            <span>${(buildingMetrics.total_volume || 0).toLocaleString('fr-FR')} m³</span>
                        </div>
                        <div class="detail-item">
                            <span>Surface de plancher:</span>
                            <span>${(surfaceMetrics.total_floor_area || buildingMetrics.total_floor_area || 0).toLocaleString('fr-FR')} m²</span>
                        </div>
                    </div>

                    <h4 style="margin-top: 30px;"><i class="fas fa-list"></i> Répartition Détaillée</h4>
                    <div class="element-details-table">
                        <table>
                            <thead>
                                <tr>
                                    <th>Type d'Élément</th>
                                    <th>Quantité</th>
                                    <th>Pourcentage</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${generateElementDetailsTable(elementCounts)}
                            </tbody>
                        </table>
                    </div>
                </div>
            `;
        }

        function generateElementDetailsTable(elementCounts) {
            if (!elementCounts || Object.keys(elementCounts).length === 0) {
                return '<tr><td colspan="3" style="text-align: center; color: #6b7280;">Aucune donnée disponible</td></tr>';
            }

            const total = elementCounts.total || Object.values(elementCounts).reduce((sum, count) => sum + (typeof count === 'number' ? count : 0), 0);
            let html = '';

            Object.entries(elementCounts).forEach(([type, count]) => {
                if (type !== 'total' && typeof count === 'number' && count > 0) {
                    const percentage = total > 0 ? ((count / total) * 100).toFixed(1) : '0.0';
                    html += `
                        <tr>
                            <td><i class="fas ${getElementIcon(type)}"></i> ${type}</td>
                            <td>${count}</td>
                            <td>${percentage}%</td>
                        </tr>
                    `;
                }
            });

            return html || '<tr><td colspan="3" style="text-align: center; color: #6b7280;">Aucune donnée disponible</td></tr>';
        }

        // Pop-up de classification avec chargement
        function showClassificationPopupWithLoading() {
            if (currentPopup) currentPopup.remove();

            const popup = document.createElement('div');
            popup.className = 'modern-popup';
            popup.innerHTML = `
                <div class="popup-content">
                    <div class="popup-header" style="background: linear-gradient(135deg, #3b82f6, #1d4ed8);">
                        <h2><i class="fas fa-building"></i> Classification IA du Bâtiment</h2>
                        <button class="popup-close" onclick="closeCurrentPopup()">×</button>
                    </div>
                    <div class="popup-body" style="text-align: center; padding: 60px;">
                        <div class="loading-spinner" style="margin: 0 auto 20px auto;"></div>
                        <h3 style="color: #3b82f6; margin-bottom: 10px;">Classification en cours...</h3>
                        <p style="color: #64748b;">Analyse IA des caractéristiques du bâtiment et classification automatique.</p>
                    </div>
                </div>
            `;

            document.body.appendChild(popup);
            currentPopup = popup;
            setTimeout(() => popup.classList.add('show'), 10);
        }

        function updateClassificationPopup(result) {
            if (!currentPopup) return;

            // Remplacer le contenu du pop-up existant par les vraies données
            showClassificationPopup(result);
        }

        function updateClassificationPopupWithError(errorMessage) {
            if (!currentPopup) return;

            const popupContent = currentPopup.querySelector('.popup-content');
            popupContent.innerHTML = `
                <div class="popup-header" style="background: linear-gradient(135deg, #dc2626, #b91c1c);">
                    <h2><i class="fas fa-exclamation-triangle"></i> Erreur de Classification</h2>
                    <button class="popup-close" onclick="closeCurrentPopup()">×</button>
                </div>
                <div class="popup-body" style="text-align: center; padding: 40px;">
                    <i class="fas fa-times-circle" style="font-size: 48px; color: #dc2626; margin-bottom: 20px;"></i>
                    <h3 style="color: #dc2626; margin-bottom: 15px;">Classification Échouée</h3>
                    <p style="color: #64748b; margin-bottom: 30px;">${errorMessage}</p>
                    <button class="btn btn-primary" onclick="closeCurrentPopup()">Fermer</button>
                </div>
            `;
        }

        // Pop-up principal de classification (copié de bim_analysis.html)
        function showClassificationPopup(result) {
            if (currentPopup) currentPopup.remove();

            const classification = result.classification;
            const features = result.features;
            const indicators = result.type_indicators;

            const popup = document.createElement('div');
            popup.className = 'modern-popup';
            popup.innerHTML = `
                <div class="popup-content">
                    <div class="popup-header" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed);">
                        <h2><i class="fas fa-brain"></i> Classification IA du Bâtiment</h2>
                        <button class="popup-close" onclick="closeCurrentPopup()">×</button>
                    </div>
                    <div class="popup-body">
                        <div class="classification-result">
                            <div class="building-type-card">
                                <div class="type-icon">
                                    <i class="fas ${getBuildingTypeIcon(classification?.building_type)}"></i>
                                </div>
                                <div class="type-info">
                                    <h3>${classification?.building_type || 'Type non déterminé'}</h3>
                                    <div class="confidence-bar">
                                        <div class="confidence-fill" style="width: ${(classification?.confidence * 100) || 0}%"></div>
                                        <span class="confidence-text">${Math.round((classification?.confidence * 100) || 0)}% de confiance</span>
                                    </div>
                                </div>
                            </div>

                            <div class="features-analysis">
                                <h4><i class="fas fa-list-check"></i> Caractéristiques Analysées</h4>
                                <div class="features-grid">
                                    ${generateFeaturesGrid(features)}
                                </div>
                            </div>

                            <div class="type-indicators">
                                <h4><i class="fas fa-chart-bar"></i> Indicateurs de Type</h4>
                                <div class="indicators-list">
                                    ${generateTypeIndicators(indicators)}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(popup);
            currentPopup = popup;
            setTimeout(() => popup.classList.add('show'), 10);
        }

        function getBuildingTypeIcon(type) {
            const icons = {
                'Résidentiel': 'fa-home',
                'Tertiaire': 'fa-building',
                'Industriel': 'fa-industry',
                'Commercial': 'fa-store',
                'Éducatif': 'fa-school',
                'Santé': 'fa-hospital'
            };
            return icons[type] || 'fa-question';
        }

        function generateFeaturesGrid(features) {
            if (!features) return '<p>Aucune caractéristique disponible</p>';

            let html = '';
            Object.entries(features).forEach(([key, value]) => {
                html += `
                    <div class="feature-item">
                        <span class="feature-name">${key}:</span>
                        <span class="feature-value">${value}</span>
                    </div>
                `;
            });
            return html;
        }

        function generateTypeIndicators(indicators) {
            if (!indicators) return '<p>Aucun indicateur disponible</p>';

            let html = '';
            Object.entries(indicators).forEach(([type, score]) => {
                const percentage = Math.round(score * 100);
                html += `
                    <div class="indicator-item">
                        <div class="indicator-header">
                            <span class="indicator-type">${type}</span>
                            <span class="indicator-score">${percentage}%</span>
                        </div>
                        <div class="indicator-bar">
                            <div class="indicator-fill" style="width: ${percentage}%"></div>
                        </div>
                    </div>
                `;
            });
            return html;
        }

        // Pop-up PMR avec chargement
        function showPMRPopupWithLoading() {
            if (currentPopup) currentPopup.remove();

            const popup = document.createElement('div');
            popup.className = 'modern-popup';
            popup.innerHTML = `
                <div class="popup-content">
                    <div class="popup-header" style="background: linear-gradient(135deg, #10b981, #059669);">
                        <h2><i class="fas fa-wheelchair"></i> Analyse PMR (Accessibilité)</h2>
                        <button class="popup-close" onclick="closeCurrentPopup()">×</button>
                    </div>
                    <div class="popup-body" style="text-align: center; padding: 60px;">
                        <div class="loading-spinner" style="margin: 0 auto 20px auto;"></div>
                        <h3 style="color: #10b981; margin-bottom: 10px;">Analyse PMR en cours...</h3>
                        <p style="color: #64748b;">Vérification de la conformité aux normes d'accessibilité.</p>
                    </div>
                </div>
            `;

            document.body.appendChild(popup);
            currentPopup = popup;
            setTimeout(() => popup.classList.add('show'), 10);
        }

        function updatePMRPopup(pmrAnalysis) {
            if (!currentPopup) return;

            console.log('🔍 DONNÉES PMR COMPLÈTES reçues:', pmrAnalysis);
            showPMRPopup(pmrAnalysis);
        }

        function updatePMRPopupWithError(errorMessage) {
            if (!currentPopup) return;

            const popupContent = currentPopup.querySelector('.popup-content');
            popupContent.innerHTML = `
                <div class="popup-header" style="background: linear-gradient(135deg, #dc2626, #b91c1c);">
                    <h2><i class="fas fa-exclamation-triangle"></i> Erreur d'Analyse PMR</h2>
                    <button class="popup-close" onclick="closeCurrentPopup()">×</button>
                </div>
                <div class="popup-body" style="text-align: center; padding: 40px;">
                    <i class="fas fa-times-circle" style="font-size: 48px; color: #dc2626; margin-bottom: 20px;"></i>
                    <h3 style="color: #dc2626; margin-bottom: 15px;">Analyse PMR Échouée</h3>
                    <p style="color: #64748b; margin-bottom: 30px;">${errorMessage}</p>
                    <button class="btn btn-primary" onclick="closeCurrentPopup()">Fermer</button>
                </div>
            `;
        }

        // Pop-up principal PMR (copié de bim_analysis.html)
        function showPMRPopup(pmrAnalysis) {
            if (currentPopup) currentPopup.remove();

            const summary = pmrAnalysis.summary;
            const checks = pmrAnalysis.pmr_checks;

            const popup = document.createElement('div');
            popup.className = 'modern-popup';
            popup.innerHTML = `
                <div class="popup-content">
                    <div class="popup-header" style="background: linear-gradient(135deg, #10b981, #059669);">
                        <h2><i class="fas fa-wheelchair"></i> Analyse PMR (Accessibilité)</h2>
                        <button class="popup-close" onclick="closeCurrentPopup()">×</button>
                    </div>
                    <div class="popup-body">
                        <div class="pmr-summary">
                            <div class="compliance-score">
                                <div class="score-circle">
                                    <div class="score-value">${Math.round(summary?.compliance_percentage || 0)}%</div>
                                    <div class="score-label">Conformité</div>
                                </div>
                                <div class="score-status ${getComplianceStatus(summary?.compliance_percentage)}">
                                    <i class="fas ${getComplianceIcon(summary?.compliance_percentage)}"></i>
                                    ${getComplianceText(summary?.compliance_percentage)}
                                </div>
                            </div>

                            <div class="pmr-stats">
                                <div class="stat-item">
                                    <div class="stat-value">${summary?.total_checks || 0}</div>
                                    <div class="stat-label">Vérifications</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value">${summary?.passed_checks || 0}</div>
                                    <div class="stat-label">Conformes</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value">${summary?.failed_checks || 0}</div>
                                    <div class="stat-label">Non conformes</div>
                                </div>
                            </div>
                        </div>

                        <div class="pmr-details">
                            <h4><i class="fas fa-list-check"></i> Détails des Vérifications</h4>
                            <div class="checks-list">
                                ${generatePMRChecksList(checks)}
                            </div>
                        </div>

                        <div class="pmr-recommendations">
                            <h4><i class="fas fa-lightbulb"></i> Recommandations</h4>
                            <div class="recommendations">
                                ${generatePMRRecommendations(summary)}
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(popup);
            currentPopup = popup;
            setTimeout(() => popup.classList.add('show'), 10);
        }

        function getComplianceStatus(percentage) {
            if (percentage >= 80) return 'excellent';
            if (percentage >= 60) return 'good';
            if (percentage >= 40) return 'fair';
            return 'poor';
        }

        function getComplianceIcon(percentage) {
            if (percentage >= 80) return 'fa-check-circle';
            if (percentage >= 60) return 'fa-exclamation-triangle';
            return 'fa-times-circle';
        }

        function getComplianceText(percentage) {
            if (percentage >= 80) return 'Excellente conformité';
            if (percentage >= 60) return 'Conformité partielle';
            if (percentage >= 40) return 'Conformité insuffisante';
            return 'Non conforme';
        }

        function generatePMRChecksList(checks) {
            if (!checks || checks.length === 0) {
                return '<p>Aucune vérification disponible</p>';
            }

            return checks.map(check => `
                <div class="check-item ${check.status}">
                    <div class="check-icon">
                        <i class="fas ${check.status === 'passed' ? 'fa-check' : 'fa-times'}"></i>
                    </div>
                    <div class="check-content">
                        <div class="check-name">${check.name}</div>
                        <div class="check-description">${check.description}</div>
                        ${check.recommendation ? `<div class="check-recommendation">${check.recommendation}</div>` : ''}
                    </div>
                </div>
            `).join('');
        }

        function generatePMRRecommendations(summary) {
            const recommendations = [
                'Vérifier la largeur des passages (minimum 1,40m)',
                'Contrôler la hauteur des équipements (0,90m à 1,30m)',
                'Valider les pentes des rampes (maximum 5%)',
                'Assurer des espaces de manœuvre suffisants'
            ];

            return recommendations.map(rec => `
                <div class="recommendation-item">
                    <i class="fas fa-arrow-right"></i>
                    <span>${rec}</span>
                </div>
            `).join('');
        }

        // Mettre à jour les métriques toutes les 3 secondes
        setInterval(updateRealTimeMetrics, 3000);
        updateRealTimeMetrics();
    </script>
</body>
</html>
