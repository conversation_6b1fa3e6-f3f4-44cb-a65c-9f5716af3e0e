<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BIMEX 2.0 - Mission Control Interface</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        :root {
            /* Couleurs futuristes */
            --primary-neon: #00f5ff;
            --secondary-neon: #ff0080;
            --success-neon: #00ff88;
            --warning-neon: #ffaa00;
            --danger-neon: #ff3366;
            --purple-neon: #8b5cf6;
            
            /* Arrière-plans sombres */
            --bg-dark: #0a0a0f;
            --bg-card: #1a1a2e;
            --bg-glass: rgba(26, 26, 46, 0.8);
            --bg-hover: rgba(0, 245, 255, 0.1);
            
            /* Texte */
            --text-primary: #ffffff;
            --text-secondary: #a0a0b0;
            --text-muted: #606070;
            
            /* Effets */
            --glow-primary: 0 0 20px rgba(0, 245, 255, 0.5);
            --glow-secondary: 0 0 20px rgba(255, 0, 128, 0.5);
            --glow-success: 0 0 20px rgba(0, 255, 136, 0.5);
            
            /* Animations */
            --transition-fast: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            --transition-smooth: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: var(--bg-dark);
            color: var(--text-primary);
            overflow-x: hidden;
            position: relative;
        }

        /* Arrière-plan animé futuriste */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(circle at 20% 80%, rgba(0, 245, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 0, 128, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(139, 92, 246, 0.1) 0%, transparent 50%);
            z-index: -2;
            animation: backgroundPulse 8s ease-in-out infinite;
        }

        @keyframes backgroundPulse {
            0%, 100% { opacity: 0.3; }
            50% { opacity: 0.6; }
        }

        /* Grille de fond style Matrix/Cyberpunk */
        body::after {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: 
                linear-gradient(rgba(0, 245, 255, 0.03) 1px, transparent 1px),
                linear-gradient(90deg, rgba(0, 245, 255, 0.03) 1px, transparent 1px);
            background-size: 50px 50px;
            z-index: -1;
            animation: gridMove 20s linear infinite;
        }

        @keyframes gridMove {
            0% { transform: translate(0, 0); }
            100% { transform: translate(50px, 50px); }
        }

        /* Container principal - Style Mission Control */
        .mission-control {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            position: relative;
        }

        /* Header futuriste */
        .control-header {
            background: linear-gradient(135deg, var(--bg-card) 0%, rgba(0, 245, 255, 0.1) 100%);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(0, 245, 255, 0.2);
            padding: 20px 40px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logo-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, var(--primary-neon), var(--secondary-neon));
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            box-shadow: var(--glow-primary);
            animation: logoGlow 3s ease-in-out infinite;
        }

        @keyframes logoGlow {
            0%, 100% { box-shadow: var(--glow-primary); }
            50% { box-shadow: var(--glow-secondary); }
        }

        .logo-text {
            display: flex;
            flex-direction: column;
        }

        .logo-title {
            font-size: 28px;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-neon), var(--secondary-neon));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .logo-subtitle {
            font-size: 12px;
            color: var(--text-secondary);
            font-family: 'JetBrains Mono', monospace;
            letter-spacing: 2px;
            text-transform: uppercase;
        }

        /* Status système en temps réel */
        .system-status {
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            background: var(--bg-glass);
            border-radius: 20px;
            border: 1px solid rgba(0, 245, 255, 0.2);
            font-family: 'JetBrains Mono', monospace;
            font-size: 12px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--success-neon);
            box-shadow: 0 0 10px var(--success-neon);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* Zone principale - Layout en grille intelligente */
        .control-main {
            flex: 1;
            padding: 40px;
            display: grid;
            grid-template-columns: 300px 1fr 350px;
            grid-template-rows: auto 1fr;
            gap: 30px;
            grid-template-areas: 
                "sidebar mission-panel info-panel"
                "sidebar mission-panel info-panel";
        }

        /* Sidebar de navigation - Style futuriste */
        .control-sidebar {
            grid-area: sidebar;
            background: var(--bg-glass);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            border: 1px solid rgba(0, 245, 255, 0.2);
            padding: 30px;
            height: fit-content;
            position: sticky;
            top: 120px;
        }

        .sidebar-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 25px;
            color: var(--primary-neon);
            text-transform: uppercase;
            letter-spacing: 1px;
            font-family: 'JetBrains Mono', monospace;
        }

        .mission-steps {
            list-style: none;
        }

        .mission-step {
            margin-bottom: 15px;
            position: relative;
        }

        .step-button {
            width: 100%;
            background: transparent;
            border: 1px solid rgba(255, 255, 255, 0.1);
            color: var(--text-secondary);
            padding: 15px 20px;
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition-smooth);
            display: flex;
            align-items: center;
            gap: 15px;
            font-size: 14px;
            position: relative;
            overflow: hidden;
        }

        .step-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0, 245, 255, 0.1), transparent);
            transition: var(--transition-smooth);
        }

        .step-button:hover::before {
            left: 100%;
        }

        .step-button:hover {
            border-color: var(--primary-neon);
            color: var(--text-primary);
            box-shadow: var(--glow-primary);
            transform: translateX(5px);
        }

        .step-button.active {
            background: linear-gradient(135deg, rgba(0, 245, 255, 0.2), rgba(255, 0, 128, 0.1));
            border-color: var(--primary-neon);
            color: var(--text-primary);
            box-shadow: var(--glow-primary);
        }

        .step-icon {
            width: 20px;
            text-align: center;
        }

        .step-status {
            margin-left: auto;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--text-muted);
            transition: var(--transition-fast);
        }

        .step-button.completed .step-status {
            background: var(--success-neon);
            box-shadow: 0 0 8px var(--success-neon);
        }

        .step-button.active .step-status {
            background: var(--primary-neon);
            box-shadow: 0 0 8px var(--primary-neon);
        }

        /* Boutons d'action rapide */
        .quick-actions {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .quick-action-btn {
            width: 100%;
            background: transparent;
            border: 1px solid rgba(255, 255, 255, 0.1);
            color: var(--text-secondary);
            padding: 12px 15px;
            border-radius: 8px;
            cursor: pointer;
            transition: var(--transition-smooth);
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 13px;
            text-align: left;
        }

        .quick-action-btn:hover {
            border-color: var(--primary-neon);
            color: var(--text-primary);
            background: rgba(0, 245, 255, 0.05);
            transform: translateX(3px);
        }

        .quick-action-btn i {
            width: 16px;
            text-align: center;
            color: var(--primary-neon);
        }

        /* Panel principal - Zone de mission */
        .mission-panel {
            grid-area: mission-panel;
            background: var(--bg-glass);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            border: 1px solid rgba(0, 245, 255, 0.2);
            padding: 40px;
            position: relative;
            overflow: hidden;
        }

        .mission-panel::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-neon), var(--secondary-neon), var(--purple-neon));
            animation: progressGlow 3s ease-in-out infinite;
        }

        @keyframes progressGlow {
            0%, 100% { opacity: 0.5; }
            50% { opacity: 1; }
        }

        /* Panel d'informations - Style HUD */
        .info-panel {
            grid-area: info-panel;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .info-widget {
            background: var(--bg-glass);
            backdrop-filter: blur(20px);
            border-radius: 16px;
            border: 1px solid rgba(0, 245, 255, 0.2);
            padding: 25px;
            position: relative;
        }

        .widget-title {
            font-size: 14px;
            font-weight: 600;
            color: var(--primary-neon);
            margin-bottom: 20px;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-family: 'JetBrains Mono', monospace;
        }

        /* Styles pour le contenu des étapes */
        .step-content {
            animation: slideIn 0.5s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .step-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .step-title {
            font-size: 32px;
            font-weight: 700;
            background: linear-gradient(135deg, var(--primary-neon), var(--secondary-neon));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }

        .step-subtitle {
            color: var(--text-secondary);
            font-size: 16px;
            max-width: 600px;
            margin: 0 auto;
        }

        /* Zone de chargement futuriste */
        .upload-zone {
            border: 2px dashed rgba(0, 245, 255, 0.3);
            border-radius: 20px;
            padding: 60px 40px;
            text-align: center;
            margin: 40px 0;
            background: rgba(0, 245, 255, 0.05);
            transition: var(--transition-smooth);
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .upload-zone::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: conic-gradient(from 0deg, transparent, rgba(0, 245, 255, 0.1), transparent);
            animation: rotate 4s linear infinite;
            opacity: 0;
            transition: var(--transition-smooth);
        }

        .upload-zone:hover::before {
            opacity: 1;
        }

        .upload-zone:hover {
            border-color: var(--primary-neon);
            background: rgba(0, 245, 255, 0.1);
            box-shadow: var(--glow-primary);
        }

        .upload-icon {
            font-size: 64px;
            color: var(--primary-neon);
            margin-bottom: 20px;
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .upload-text h3 {
            font-size: 24px;
            color: var(--text-primary);
            margin-bottom: 10px;
        }

        .upload-text p {
            color: var(--text-secondary);
            margin-bottom: 15px;
        }

        .upload-text small {
            color: var(--text-muted);
            font-size: 12px;
        }

        /* Grille de métriques futuriste */
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .metric-card {
            background: var(--bg-glass);
            border-radius: 16px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
            overflow: hidden;
            transition: var(--transition-smooth);
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .metric-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--primary-neon);
            transform: scaleX(0);
            transition: var(--transition-smooth);
        }

        .metric-card:hover::before {
            transform: scaleX(1);
        }

        .metric-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--glow-primary);
        }

        .metric-card.neon-blue::before { background: var(--primary-neon); }
        .metric-card.neon-green::before { background: var(--success-neon); }
        .metric-card.neon-purple::before { background: var(--purple-neon); }
        .metric-card.neon-orange::before { background: var(--warning-neon); }

        .metric-icon {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            background: rgba(0, 245, 255, 0.1);
            color: var(--primary-neon);
        }

        .metric-card.neon-green .metric-icon {
            background: rgba(0, 255, 136, 0.1);
            color: var(--success-neon);
        }

        .metric-card.neon-purple .metric-icon {
            background: rgba(139, 92, 246, 0.1);
            color: var(--purple-neon);
        }

        .metric-card.neon-orange .metric-icon {
            background: rgba(255, 170, 0, 0.1);
            color: var(--warning-neon);
        }

        .metric-data {
            flex: 1;
        }

        .metric-value {
            font-size: 28px;
            font-weight: 700;
            color: var(--text-primary);
            font-family: 'JetBrains Mono', monospace;
            margin-bottom: 5px;
        }

        .metric-label {
            color: var(--text-secondary);
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .metric-trend {
            color: var(--success-neon);
            font-size: 18px;
        }

        /* Barre de progression futuriste */
        .analysis-progress {
            margin: 40px 0;
            padding: 30px;
            background: var(--bg-glass);
            border-radius: 16px;
            border: 1px solid rgba(0, 245, 255, 0.2);
        }

        .progress-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .progress-header h3 {
            color: var(--text-primary);
            font-size: 18px;
        }

        .progress-percentage {
            color: var(--primary-neon);
            font-family: 'JetBrains Mono', monospace;
            font-weight: 600;
            font-size: 16px;
        }

        .progress-bar {
            height: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 15px;
            position: relative;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--primary-neon), var(--secondary-neon));
            border-radius: 4px;
            width: 0%;
            transition: width 0.5s ease;
            position: relative;
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .progress-status {
            color: var(--text-secondary);
            font-size: 14px;
            font-family: 'JetBrains Mono', monospace;
        }

        /* Boutons de contrôle futuristes */
        .mission-controls {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin-top: 40px;
        }

        .control-btn {
            padding: 15px 30px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-smooth);
            display: flex;
            align-items: center;
            gap: 10px;
            position: relative;
            overflow: hidden;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .control-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: var(--transition-smooth);
        }

        .control-btn:hover::before {
            left: 100%;
        }

        .control-btn.primary {
            background: linear-gradient(135deg, var(--primary-neon), var(--secondary-neon));
            color: white;
            box-shadow: var(--glow-primary);
        }

        .control-btn.primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 0 30px rgba(0, 245, 255, 0.7);
        }

        .control-btn.secondary {
            background: transparent;
            color: var(--text-primary);
            border: 2px solid rgba(0, 245, 255, 0.3);
        }

        .control-btn.secondary:hover {
            border-color: var(--primary-neon);
            box-shadow: var(--glow-primary);
            transform: translateY(-2px);
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .control-main {
                grid-template-columns: 1fr;
                grid-template-areas:
                    "mission-panel"
                    "sidebar"
                    "info-panel";
            }

            .control-sidebar {
                position: static;
            }
        }

        @media (max-width: 768px) {
            .control-header {
                padding: 15px 20px;
            }

            .control-main {
                padding: 20px;
                gap: 20px;
            }

            .logo-title {
                font-size: 24px;
            }

            .system-status {
                gap: 10px;
            }

            .metrics-grid {
                grid-template-columns: 1fr;
            }

            .mission-controls {
                flex-direction: column;
            }
        }

        /* Styles pour l'interface de classification IA */
        .ai-brain-visual {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin: 40px 0;
            padding: 40px;
            background: var(--bg-glass);
            border-radius: 20px;
            border: 1px solid rgba(139, 92, 246, 0.3);
        }

        .neural-network {
            display: flex;
            gap: 80px;
            align-items: center;
        }

        .neural-layer {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .neuron {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: rgba(139, 92, 246, 0.3);
            border: 2px solid var(--purple-neon);
            position: relative;
            transition: var(--transition-smooth);
        }

        .neuron.active {
            background: var(--purple-neon);
            box-shadow: 0 0 15px var(--purple-neon);
            animation: neuronPulse 2s infinite;
        }

        @keyframes neuronPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.2); }
        }

        .classification-result {
            text-align: center;
            flex: 1;
            margin-left: 40px;
        }

        .building-type {
            font-size: 24px;
            font-weight: 700;
            color: var(--primary-neon);
            margin: 20px 0;
            font-family: 'JetBrains Mono', monospace;
        }

        .confidence-meter {
            background: rgba(255, 255, 255, 0.1);
            height: 8px;
            border-radius: 4px;
            overflow: hidden;
            position: relative;
            margin: 20px 0;
        }

        .confidence-bar {
            height: 100%;
            background: linear-gradient(90deg, var(--danger-neon), var(--warning-neon), var(--success-neon));
            width: 0%;
            transition: width 1s ease;
        }

        .confidence-text {
            color: var(--text-primary);
            font-family: 'JetBrains Mono', monospace;
            font-weight: 600;
        }

        /* Styles pour l'interface des coûts */
        .costs-dashboard {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin: 40px 0;
        }

        .cost-summary {
            background: var(--bg-glass);
            border-radius: 20px;
            padding: 40px;
            border: 1px solid rgba(0, 245, 255, 0.3);
            text-align: center;
        }

        .cost-main {
            margin-bottom: 30px;
        }

        .cost-value {
            font-size: 48px;
            font-weight: 800;
            color: var(--primary-neon);
            font-family: 'JetBrains Mono', monospace;
            text-shadow: 0 0 20px rgba(0, 245, 255, 0.5);
        }

        .cost-label {
            color: var(--text-secondary);
            font-size: 16px;
            margin-top: 10px;
        }

        .cost-details {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .cost-item {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .cost-breakdown {
            background: var(--bg-glass);
            border-radius: 20px;
            padding: 40px;
            border: 1px solid rgba(255, 0, 128, 0.3);
        }

        /* Styles pour l'interface environnementale */
        .environment-dashboard {
            display: flex;
            gap: 40px;
            margin: 40px 0;
            align-items: center;
        }

        .sustainability-score {
            text-align: center;
            background: var(--bg-glass);
            border-radius: 20px;
            padding: 40px;
            border: 1px solid rgba(0, 255, 136, 0.3);
        }

        .score-circle {
            position: relative;
            margin-bottom: 20px;
        }

        .score-ring {
            transform: rotate(-90deg);
        }

        .score-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            display: flex;
            align-items: baseline;
            gap: 5px;
        }

        .score-value {
            font-size: 32px;
            font-weight: 800;
            color: var(--success-neon);
            font-family: 'JetBrains Mono', monospace;
        }

        .score-max {
            font-size: 16px;
            color: var(--text-secondary);
        }

        .environmental-metrics {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .env-metric {
            display: flex;
            align-items: center;
            gap: 20px;
            background: var(--bg-glass);
            padding: 25px;
            border-radius: 16px;
            border: 1px solid rgba(0, 255, 136, 0.2);
        }

        .env-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            background: rgba(0, 255, 136, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--success-neon);
            font-size: 20px;
        }

        .env-value {
            font-size: 24px;
            font-weight: 700;
            color: var(--text-primary);
            font-family: 'JetBrains Mono', monospace;
        }

        .env-label {
            color: var(--text-secondary);
            font-size: 14px;
        }

        /* Styles pour l'interface d'optimisation */
        .optimization-lab {
            margin: 40px 0;
        }

        .genetic-algorithm {
            background: var(--bg-glass);
            border-radius: 20px;
            padding: 40px;
            border: 1px solid rgba(255, 170, 0, 0.3);
            margin-bottom: 30px;
        }

        .generations-display {
            display: flex;
            align-items: center;
            gap: 40px;
            margin-top: 20px;
        }

        .generation {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .generation-number, .fitness-score {
            font-family: 'JetBrains Mono', monospace;
            color: var(--text-primary);
        }

        .generation-number span, .fitness-score span {
            color: var(--warning-neon);
            font-weight: 700;
        }

        .evolution-progress {
            flex: 1;
            height: 12px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 6px;
            overflow: hidden;
        }

        .evolution-bar {
            height: 100%;
            background: linear-gradient(90deg, var(--warning-neon), var(--success-neon));
            width: 0%;
            transition: width 0.5s ease;
        }

        .pareto-solutions {
            background: var(--bg-glass);
            border-radius: 20px;
            padding: 40px;
            border: 1px solid rgba(255, 0, 128, 0.3);
        }

        .solutions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        /* Styles pour l'interface de rapport */
        .report-generator {
            display: flex;
            gap: 40px;
            margin: 40px 0;
            align-items: flex-start;
        }

        .report-preview {
            background: var(--bg-glass);
            border-radius: 20px;
            padding: 40px;
            border: 1px solid rgba(255, 0, 128, 0.3);
            text-align: center;
            flex: 1;
        }

        .document-icon {
            font-size: 80px;
            color: var(--secondary-neon);
            margin-bottom: 20px;
            animation: documentFloat 3s ease-in-out infinite;
        }

        @keyframes documentFloat {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .report-sections {
            flex: 1;
            background: var(--bg-glass);
            border-radius: 20px;
            padding: 40px;
            border: 1px solid rgba(0, 245, 255, 0.3);
        }

        .section-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            color: var(--text-secondary);
        }

        .section-item.completed {
            color: var(--success-neon);
        }

        .section-item.completed i {
            color: var(--success-neon);
        }

        /* Scanner d'anomalies - Style radar */
        .anomalies-scanner {
            margin: 40px 0;
        }

        .scanner-visual {
            display: flex;
            align-items: center;
            gap: 40px;
            background: var(--bg-glass);
            border-radius: 20px;
            padding: 40px;
            border: 1px solid rgba(255, 51, 102, 0.3);
            margin-bottom: 30px;
        }

        .radar-container {
            width: 200px;
            height: 200px;
            border-radius: 50%;
            border: 2px solid var(--danger-neon);
            position: relative;
            background: radial-gradient(circle, rgba(255, 51, 102, 0.1) 0%, transparent 70%);
        }

        .radar-sweep {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 2px;
            height: 90px;
            background: linear-gradient(to top, transparent, var(--danger-neon));
            transform-origin: bottom center;
            transform: translate(-50%, -100%) rotate(0deg);
            animation: radarSweep 3s linear infinite;
        }

        @keyframes radarSweep {
            0% { transform: translate(-50%, -100%) rotate(0deg); }
            100% { transform: translate(-50%, -100%) rotate(360deg); }
        }

        .radar-grid {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 100%;
            height: 100%;
            border-radius: 50%;
            border: 1px solid rgba(255, 51, 102, 0.3);
        }

        .radar-grid::before,
        .radar-grid::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            border-radius: 50%;
            border: 1px solid rgba(255, 51, 102, 0.2);
        }

        .radar-grid::before {
            width: 66%;
            height: 66%;
        }

        .radar-grid::after {
            width: 33%;
            height: 33%;
        }

        .radar-center {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--danger-neon);
            box-shadow: 0 0 15px var(--danger-neon);
        }

        .scanner-status {
            flex: 1;
            text-align: center;
        }

        .scanner-status h3 {
            color: var(--danger-neon);
            font-size: 24px;
            margin-bottom: 10px;
        }

        .severity-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
        }

        .severity-card {
            background: var(--bg-glass);
            border-radius: 16px;
            padding: 25px;
            text-align: center;
            border: 2px solid;
            transition: var(--transition-smooth);
        }

        .severity-card.critical {
            border-color: var(--danger-neon);
        }

        .severity-card.high {
            border-color: var(--warning-neon);
        }

        .severity-card.medium {
            border-color: var(--primary-neon);
        }

        .severity-card.low {
            border-color: var(--success-neon);
        }

        .severity-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 0 20px rgba(255, 255, 255, 0.1);
        }

        .severity-icon {
            font-size: 32px;
            margin-bottom: 15px;
        }

        .severity-card.critical .severity-icon {
            color: var(--danger-neon);
        }

        .severity-card.high .severity-icon {
            color: var(--warning-neon);
        }

        .severity-card.medium .severity-icon {
            color: var(--primary-neon);
        }

        .severity-card.low .severity-icon {
            color: var(--success-neon);
        }

        .severity-count {
            font-size: 36px;
            font-weight: 800;
            font-family: 'JetBrains Mono', monospace;
            color: var(--text-primary);
            margin-bottom: 10px;
        }

        .severity-label {
            color: var(--text-secondary);
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        /* Styles pour les métriques temps réel */
        .metric-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .metric-item:last-child {
            border-bottom: none;
        }

        .metric-name {
            color: var(--text-secondary);
            font-size: 14px;
            font-family: 'JetBrains Mono', monospace;
        }

        .metric-value {
            color: var(--primary-neon);
            font-weight: 600;
            font-family: 'JetBrains Mono', monospace;
        }

        /* Animations supplémentaires */
        .mission-panel {
            transition: var(--transition-smooth);
        }

        /* Effets de survol pour les widgets */
        .info-widget:hover {
            transform: translateY(-2px);
            box-shadow: var(--glow-primary);
        }

        /* Styles pour le sélecteur de projet principal */
        .project-selector-main {
            margin: 40px 0;
            text-align: center;
            background: var(--bg-glass);
            padding: 30px;
            border-radius: 20px;
            border: 1px solid rgba(0, 245, 255, 0.2);
        }

        .project-selector-main h3 {
            color: var(--text-primary);
            margin-bottom: 25px;
            font-size: 20px;
            background: linear-gradient(135deg, var(--primary-neon), var(--secondary-neon));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .project-dropdown {
            position: relative;
            max-width: 400px;
            margin: 0 auto;
        }

        #project-select {
            width: 100%;
            padding: 15px 20px;
            background: var(--bg-card);
            border: 2px solid rgba(0, 245, 255, 0.3);
            border-radius: 12px;
            color: var(--text-primary);
            font-size: 16px;
            font-family: 'Inter', sans-serif;
            cursor: pointer;
            transition: var(--transition-smooth);
        }

        #project-select:focus {
            outline: none;
            border-color: var(--primary-neon);
            box-shadow: var(--glow-primary);
        }

        #project-select option {
            background: var(--bg-card);
            color: var(--text-primary);
            padding: 10px;
        }

        /* Styles pour les projets existants */
        .project-selector {
            margin: 40px 0;
            text-align: center;
        }

        .project-selector h3 {
            color: var(--text-primary);
            margin-bottom: 20px;
            font-size: 18px;
        }

        .projects-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .project-card {
            background: var(--bg-glass);
            border-radius: 12px;
            padding: 20px;
            border: 1px solid rgba(0, 245, 255, 0.2);
            cursor: pointer;
            transition: var(--transition-smooth);
            text-align: center;
        }

        .project-card:hover {
            border-color: var(--primary-neon);
            box-shadow: var(--glow-primary);
            transform: translateY(-3px);
        }

        .project-icon {
            font-size: 32px;
            color: var(--primary-neon);
            margin-bottom: 15px;
        }

        .project-name {
            color: var(--text-primary);
            font-weight: 600;
            margin-bottom: 5px;
        }

        .project-info {
            color: var(--text-secondary);
            font-size: 12px;
        }

        /* Styles pour l'assistant IA */
        .ai-status {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 15px;
            background: rgba(0, 245, 255, 0.1);
            border-radius: 12px;
            border: 1px solid rgba(0, 245, 255, 0.3);
        }

        .ai-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-neon), var(--secondary-neon));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
        }

        .ai-message {
            flex: 1;
        }

        .ai-name {
            color: var(--primary-neon);
            font-weight: 600;
            font-size: 14px;
            margin-bottom: 5px;
        }

        .ai-text {
            color: var(--text-secondary);
            font-size: 12px;
            line-height: 1.4;
        }

        /* Responsive amélioré */
        @media (max-width: 1400px) {
            .costs-dashboard,
            .environment-dashboard,
            .report-generator {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .ai-brain-visual,
            .scanner-visual {
                flex-direction: column;
                text-align: center;
            }

            .neural-network {
                gap: 40px;
            }

            .classification-result {
                margin-left: 0;
                margin-top: 20px;
            }

            .severity-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 480px) {
            .severity-grid {
                grid-template-columns: 1fr;
            }

            .step-title {
                font-size: 24px;
            }

            .cost-value {
                font-size: 36px;
            }
        }

        /* Styles pour les pop-ups d'analyse */
        .analysis-popup {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 10000;
            opacity: 0;
            visibility: hidden;
            transition: var(--transition-smooth);
        }

        .analysis-popup.active {
            opacity: 1;
            visibility: visible;
        }

        .popup-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
        }

        .popup-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: var(--bg-card);
            border-radius: 20px;
            border: 1px solid rgba(0, 245, 255, 0.3);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
            max-width: 800px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .popup-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 25px 30px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            background: linear-gradient(135deg, rgba(0, 245, 255, 0.1), rgba(255, 0, 128, 0.1));
        }

        .popup-header h3 {
            color: var(--text-primary);
            font-size: 20px;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .popup-close {
            background: none;
            border: none;
            color: var(--text-secondary);
            font-size: 20px;
            cursor: pointer;
            padding: 5px;
            border-radius: 50%;
            transition: var(--transition-fast);
        }

        .popup-close:hover {
            color: var(--text-primary);
            background: rgba(255, 255, 255, 0.1);
        }

        .popup-body {
            padding: 30px;
        }

        .analysis-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .analysis-card {
            background: var(--bg-glass);
            padding: 20px;
            border-radius: 12px;
            border: 1px solid rgba(0, 245, 255, 0.2);
            text-align: center;
        }

        .analysis-card h4 {
            color: var(--text-secondary);
            font-size: 14px;
            margin-bottom: 15px;
        }

        .metric-large {
            font-size: 32px;
            font-weight: 700;
            color: var(--primary-neon);
            font-family: 'JetBrains Mono', monospace;
        }

        .pmr-analysis {
            text-align: center;
        }

        .compliance-score {
            margin-bottom: 30px;
        }

        .score-circle {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            border: 4px solid var(--primary-neon);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 20px auto;
            background: rgba(0, 245, 255, 0.1);
        }

        .score-value {
            font-size: 24px;
            font-weight: 700;
            color: var(--primary-neon);
        }

        .classification-result {
            text-align: center;
        }

        .building-type-result {
            margin: 20px 0;
            padding: 20px;
            background: var(--bg-glass);
            border-radius: 12px;
        }

        .type-label {
            color: var(--text-secondary);
            margin-right: 10px;
        }

        .type-value {
            color: var(--primary-neon);
            font-weight: 600;
            font-size: 18px;
        }

        .confidence-bar {
            margin-top: 20px;
        }

        .confidence-bar .bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            overflow: hidden;
            margin-top: 10px;
        }

        .confidence-bar .fill {
            height: 100%;
            background: linear-gradient(90deg, var(--primary-neon), var(--secondary-neon));
            transition: width 1s ease;
        }
    </style>
</head>
<body>
    <div class="mission-control">
        <!-- Header futuriste -->
        <header class="control-header">
            <div class="logo-section">
                <div class="logo-icon">
                    <i class="fas fa-cube"></i>
                </div>
                <div class="logo-text">
                    <div class="logo-title">BIMEX 2.0</div>
                    <div class="logo-subtitle">Mission Control</div>
                </div>
            </div>
            
            <div class="system-status">
                <div class="status-indicator">
                    <div class="status-dot"></div>
                    <span>SYSTÈME OPÉRATIONNEL</span>
                </div>
                <div class="status-indicator">
                    <i class="fas fa-wifi"></i>
                    <span>IA CONNECTÉE</span>
                </div>
                <div class="status-indicator">
                    <i class="fas fa-clock"></i>
                    <span id="current-time">--:--:--</span>
                </div>
            </div>
        </header>

        <!-- Zone principale -->
        <main class="control-main">
            <!-- Sidebar de navigation -->
            <aside class="control-sidebar">
                <h3 class="sidebar-title">Séquence de Mission</h3>
                <ul class="mission-steps">
                    <li class="mission-step">
                        <button class="step-button active" data-step="upload">
                            <i class="fas fa-upload step-icon"></i>
                            <span>Chargement Modèle</span>
                            <div class="step-status"></div>
                        </button>
                    </li>
                    <li class="mission-step">
                        <button class="step-button" data-step="analyze">
                            <i class="fas fa-search step-icon"></i>
                            <span>Analyse Complète</span>
                            <div class="step-status"></div>
                        </button>
                    </li>
                    <li class="mission-step">
                        <button class="step-button" data-step="anomalies">
                            <i class="fas fa-exclamation-triangle step-icon"></i>
                            <span>Détection Anomalies</span>
                            <div class="step-status"></div>
                        </button>
                    </li>
                    <li class="mission-step">
                        <button class="step-button" data-step="classification">
                            <i class="fas fa-brain step-icon"></i>
                            <span>Classification IA</span>
                            <div class="step-status"></div>
                        </button>
                    </li>
                    <li class="mission-step">
                        <button class="step-button" data-step="costs">
                            <i class="fas fa-coins step-icon"></i>
                            <span>Prédiction Coûts</span>
                            <div class="step-status"></div>
                        </button>
                    </li>
                    <li class="mission-step">
                        <button class="step-button" data-step="environment">
                            <i class="fas fa-leaf step-icon"></i>
                            <span>Analyse Environnementale</span>
                            <div class="step-status"></div>
                        </button>
                    </li>
                    <li class="mission-step">
                        <button class="step-button" data-step="optimization">
                            <i class="fas fa-magic step-icon"></i>
                            <span>Optimisation IA</span>
                            <div class="step-status"></div>
                        </button>
                    </li>
                    <li class="mission-step">
                        <button class="step-button" data-step="report">
                            <i class="fas fa-file-pdf step-icon"></i>
                            <span>Rapport Final</span>
                            <div class="step-status"></div>
                        </button>
                    </li>
                </ul>

                <!-- Actions rapides -->
                <div class="quick-actions" style="margin-top: 30px; padding-top: 20px; border-top: 1px solid rgba(255,255,255,0.1);">
                    <h4 style="color: var(--text-secondary); font-size: 12px; text-transform: uppercase; letter-spacing: 1px; margin-bottom: 15px;">Actions Rapides</h4>

                    <button class="quick-action-btn" onclick="showDetailedAnalysis()" title="Analyse détaillée">
                        <i class="fas fa-chart-bar"></i>
                        <span>Analyse Détaillée</span>
                    </button>

                    <button class="quick-action-btn" onclick="showPMRAnalysis()" title="Analyse PMR">
                        <i class="fas fa-wheelchair"></i>
                        <span>Analyse PMR</span>
                    </button>

                    <button class="quick-action-btn" onclick="showClassification()" title="Classification IA">
                        <i class="fas fa-brain"></i>
                        <span>Classification</span>
                    </button>

                    <button class="quick-action-btn" onclick="generateReport()" title="Générer rapport">
                        <i class="fas fa-file-pdf"></i>
                        <span>Rapport PDF</span>
                    </button>
                </div>
            </aside>

            <!-- Panel principal -->
            <section class="mission-panel" id="mission-content">
                <!-- Le contenu sera injecté ici dynamiquement -->
            </section>

            <!-- Panel d'informations -->
            <aside class="info-panel">
                <div class="info-widget">
                    <h4 class="widget-title">Statut Projet</h4>
                    <div id="project-status">
                        <div class="metric-item">
                            <span class="metric-name">Projet</span>
                            <span class="metric-value">En attente</span>
                        </div>
                        <div class="metric-item">
                            <span class="metric-name">Étapes</span>
                            <span class="metric-value">0/8</span>
                        </div>
                        <div class="metric-item">
                            <span class="metric-name">Progression</span>
                            <span class="metric-value">0%</span>
                        </div>
                    </div>
                </div>

                <div class="info-widget">
                    <h4 class="widget-title">Métriques Temps Réel</h4>
                    <div id="real-time-metrics">
                        <!-- Métriques seront mises à jour par JavaScript -->
                    </div>
                </div>

                <div class="info-widget">
                    <h4 class="widget-title">Assistant IA</h4>
                    <div id="ai-assistant">
                        <div class="ai-status">
                            <div class="ai-avatar">
                                <i class="fas fa-robot"></i>
                            </div>
                            <div class="ai-message">
                                <div class="ai-name">BIMEX Assistant</div>
                                <div class="ai-text">Prêt à vous assister dans votre analyse BIM. Chargez un modèle pour commencer !</div>
                            </div>
                        </div>
                    </div>
                </div>
            </aside>
        </main>
    </div>

    <script>
        // Horloge temps réel
        function updateTime() {
            const now = new Date();
            document.getElementById('current-time').textContent = now.toLocaleTimeString('fr-FR');
        }
        setInterval(updateTime, 1000);
        updateTime();

        // Navigation entre les étapes
        document.querySelectorAll('.step-button').forEach(button => {
            button.addEventListener('click', function() {
                const step = this.dataset.step;
                switchToStep(step);
            });
        });

        function switchToStep(step) {
            // Mettre à jour l'état actif
            document.querySelectorAll('.step-button').forEach(btn => btn.classList.remove('active'));
            document.querySelector(`[data-step="${step}"]`).classList.add('active');
            
            // Charger le contenu de l'étape
            loadStepContent(step);
        }

        function loadStepContent(step) {
            const content = document.getElementById('mission-content');

            // Contenu selon l'étape avec animations et interactivité
            const stepContents = {
                upload: createUploadInterface(),
                analyze: createAnalysisInterface(),
                anomalies: createAnomaliesInterface(),
                classification: createClassificationInterface(),
                costs: createCostsInterface(),
                environment: createEnvironmentInterface(),
                optimization: createOptimizationInterface(),
                report: createReportInterface()
            };

            // Animation de transition
            content.style.opacity = '0';
            content.style.transform = 'translateY(20px)';

            setTimeout(() => {
                content.innerHTML = stepContents[step] || '<div>Contenu en cours de développement...</div>';
                content.style.opacity = '1';
                content.style.transform = 'translateY(0)';
            }, 200);
        }

        // Interface de chargement futuriste avec intégration réelle
        function createUploadInterface() {
            return `
                <div class="step-content">
                    <div class="step-header">
                        <h2 class="step-title">
                            <i class="fas fa-rocket"></i>
                            Initialisation de Mission
                        </h2>
                        <p class="step-subtitle">Sélectionnez votre projet BIM pour commencer l'analyse intelligente</p>
                    </div>

                    <div class="project-selector-main">
                        <h3>Sélectionnez un Projet BIM</h3>
                        <div class="project-dropdown">
                            <select id="project-select" onchange="onProjectSelected()">
                                <option value="">-- Choisir un projet --</option>
                            </select>
                        </div>
                    </div>

                    <div class="upload-zone" id="upload-zone" style="display: none;">
                        <div class="upload-visual">
                            <div class="upload-icon">
                                <i class="fas fa-cloud-upload-alt"></i>
                            </div>
                            <div class="upload-text">
                                <h3>Glissez votre fichier IFC ici</h3>
                                <p>ou cliquez pour sélectionner</p>
                                <small>Formats supportés: .ifc, .ifczip (max 500MB)</small>
                            </div>
                        </div>
                        <input type="file" id="file-input" accept=".ifc,.ifczip" hidden>
                    </div>

                    <div class="mission-controls">
                        <button class="control-btn primary" id="start-mission-btn" onclick="startMission()" disabled>
                            <i class="fas fa-play"></i>
                            Démarrer l'Analyse IA
                        </button>
                        <button class="control-btn secondary" onclick="toggleUploadMode()">
                            <i class="fas fa-upload"></i>
                            Nouveau Fichier
                        </button>
                    </div>
                </div>
            `;
        }

        // Interface d'analyse avec visualisations temps réel
        function createAnalysisInterface() {
            return `
                <div class="step-content">
                    <div class="step-header">
                        <h2 class="step-title">
                            <i class="fas fa-search"></i>
                            Analyse Structurelle Avancée
                        </h2>
                        <p class="step-subtitle">Extraction intelligente des métriques et caractéristiques du modèle</p>
                    </div>

                    <div class="analysis-dashboard">
                        <div class="metrics-grid">
                            <div class="metric-card neon-blue">
                                <div class="metric-icon"><i class="fas fa-cube"></i></div>
                                <div class="metric-data">
                                    <div class="metric-value" id="total-elements">---</div>
                                    <div class="metric-label">Éléments BIM</div>
                                </div>
                                <div class="metric-trend">
                                    <i class="fas fa-arrow-up"></i>
                                </div>
                            </div>

                            <div class="metric-card neon-green">
                                <div class="metric-icon"><i class="fas fa-expand-arrows-alt"></i></div>
                                <div class="metric-data">
                                    <div class="metric-value" id="total-area">---</div>
                                    <div class="metric-label">Surface (m²)</div>
                                </div>
                                <div class="metric-trend">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                            </div>

                            <div class="metric-card neon-purple">
                                <div class="metric-icon"><i class="fas fa-layer-group"></i></div>
                                <div class="metric-data">
                                    <div class="metric-value" id="total-volume">---</div>
                                    <div class="metric-label">Volume (m³)</div>
                                </div>
                                <div class="metric-trend">
                                    <i class="fas fa-cube"></i>
                                </div>
                            </div>

                            <div class="metric-card neon-orange">
                                <div class="metric-icon"><i class="fas fa-palette"></i></div>
                                <div class="metric-data">
                                    <div class="metric-value" id="total-materials">---</div>
                                    <div class="metric-label">Matériaux</div>
                                </div>
                                <div class="metric-trend">
                                    <i class="fas fa-industry"></i>
                                </div>
                            </div>
                        </div>

                        <div class="analysis-progress">
                            <div class="progress-header">
                                <h3>Progression de l'Analyse</h3>
                                <span class="progress-percentage">0%</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" id="analysis-progress"></div>
                            </div>
                            <div class="progress-status" id="analysis-status">En attente...</div>
                        </div>

                        <div class="analysis-visualization">
                            <canvas id="analysis-chart" width="800" height="400"></canvas>
                        </div>
                    </div>

                    <div class="mission-controls">
                        <button class="control-btn primary" onclick="startAnalysis()">
                            <i class="fas fa-play"></i>
                            Lancer l'Analyse
                        </button>
                        <button class="control-btn secondary" onclick="viewDetails()">
                            <i class="fas fa-chart-bar"></i>
                            Voir Détails
                        </button>
                    </div>
                </div>
            `;
        }

        // Interface de détection d'anomalies avec IA
        function createAnomaliesInterface() {
            return `
                <div class="step-content">
                    <div class="step-header">
                        <h2 class="step-title">
                            <i class="fas fa-shield-alt"></i>
                            Détection d'Anomalies IA
                        </h2>
                        <p class="step-subtitle">Identification intelligente des conflits et problèmes potentiels</p>
                    </div>

                    <div class="anomalies-scanner">
                        <div class="scanner-visual">
                            <div class="radar-container">
                                <div class="radar-sweep"></div>
                                <div class="radar-grid"></div>
                                <div class="radar-center"></div>
                            </div>
                            <div class="scanner-status">
                                <h3>Scanner IA Actif</h3>
                                <p>Analyse en cours...</p>
                            </div>
                        </div>

                        <div class="anomalies-results">
                            <div class="severity-grid">
                                <div class="severity-card critical">
                                    <div class="severity-icon"><i class="fas fa-exclamation-triangle"></i></div>
                                    <div class="severity-count" id="critical-count">0</div>
                                    <div class="severity-label">Critiques</div>
                                </div>
                                <div class="severity-card high">
                                    <div class="severity-icon"><i class="fas fa-exclamation-circle"></i></div>
                                    <div class="severity-count" id="high-count">0</div>
                                    <div class="severity-label">Élevées</div>
                                </div>
                                <div class="severity-card medium">
                                    <div class="severity-icon"><i class="fas fa-info-circle"></i></div>
                                    <div class="severity-count" id="medium-count">0</div>
                                    <div class="severity-label">Moyennes</div>
                                </div>
                                <div class="severity-card low">
                                    <div class="severity-icon"><i class="fas fa-check-circle"></i></div>
                                    <div class="severity-count" id="low-count">0</div>
                                    <div class="severity-label">Faibles</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mission-controls">
                        <button class="control-btn primary" onclick="startAnomalyDetection()">
                            <i class="fas fa-search"></i>
                            Scanner les Anomalies
                        </button>
                        <button class="control-btn secondary" onclick="viewAnomaliesReport()">
                            <i class="fas fa-list"></i>
                            Rapport Détaillé
                        </button>
                    </div>
                </div>
            `;
        }

        // Interfaces pour les autres étapes
        function createClassificationInterface() {
            return `
                <div class="step-content">
                    <div class="step-header">
                        <h2 class="step-title">
                            <i class="fas fa-brain"></i>
                            Classification IA Avancée
                        </h2>
                        <p class="step-subtitle">Identification automatique du type de bâtiment par deep learning</p>
                    </div>

                    <div class="ai-brain-visual">
                        <div class="neural-network">
                            <div class="neural-layer input-layer">
                                <div class="neuron active"></div>
                                <div class="neuron active"></div>
                                <div class="neuron active"></div>
                                <div class="neuron active"></div>
                            </div>
                            <div class="neural-layer hidden-layer">
                                <div class="neuron"></div>
                                <div class="neuron"></div>
                                <div class="neuron"></div>
                            </div>
                            <div class="neural-layer output-layer">
                                <div class="neuron"></div>
                                <div class="neuron"></div>
                            </div>
                        </div>
                        <div class="classification-result">
                            <h3>Résultat de Classification</h3>
                            <div class="building-type" id="building-type">En cours d'analyse...</div>
                            <div class="confidence-meter">
                                <div class="confidence-bar" id="confidence-bar"></div>
                                <span class="confidence-text" id="confidence-text">0%</span>
                            </div>
                        </div>
                    </div>

                    <div class="mission-controls">
                        <button class="control-btn primary" onclick="startClassification()">
                            <i class="fas fa-brain"></i>
                            Analyser avec IA
                        </button>
                    </div>
                </div>
            `;
        }

        function createCostsInterface() {
            return `
                <div class="step-content">
                    <div class="step-header">
                        <h2 class="step-title">
                            <i class="fas fa-coins"></i>
                            Prédiction Intelligente des Coûts
                        </h2>
                        <p class="step-subtitle">Estimation automatique basée sur l'analyse du modèle IFC</p>
                    </div>

                    <div class="costs-dashboard">
                        <div class="cost-summary">
                            <div class="cost-main">
                                <div class="cost-value" id="total-cost">€ ---</div>
                                <div class="cost-label">Coût Total Estimé</div>
                            </div>
                            <div class="cost-details">
                                <div class="cost-item">
                                    <span>Coût par m²</span>
                                    <span id="cost-per-m2">€ ---</span>
                                </div>
                                <div class="cost-item">
                                    <span>Confiance IA</span>
                                    <span id="cost-confidence">---%</span>
                                </div>
                            </div>
                        </div>

                        <div class="cost-breakdown">
                            <h3>Répartition des Coûts</h3>
                            <div class="breakdown-chart" id="cost-chart">
                                <!-- Graphique circulaire des coûts -->
                            </div>
                        </div>
                    </div>

                    <div class="mission-controls">
                        <button class="control-btn primary" onclick="predictCosts()">
                            <i class="fas fa-calculator"></i>
                            Calculer les Coûts
                        </button>
                    </div>
                </div>
            `;
        }

        function createEnvironmentInterface() {
            return `
                <div class="step-content">
                    <div class="step-header">
                        <h2 class="step-title">
                            <i class="fas fa-leaf"></i>
                            Analyse Environnementale & Durabilité
                        </h2>
                        <p class="step-subtitle">Évaluation de l'impact environnemental et suggestions d'optimisation</p>
                    </div>

                    <div class="environment-dashboard">
                        <div class="sustainability-score">
                            <div class="score-circle">
                                <svg class="score-ring" width="120" height="120">
                                    <circle cx="60" cy="60" r="50" stroke="rgba(0,255,136,0.2)" stroke-width="8" fill="none"/>
                                    <circle cx="60" cy="60" r="50" stroke="var(--success-neon)" stroke-width="8" fill="none"
                                            stroke-dasharray="314" stroke-dashoffset="314" id="score-progress"/>
                                </svg>
                                <div class="score-text">
                                    <div class="score-value" id="sustainability-score">-</div>
                                    <div class="score-max">/10</div>
                                </div>
                            </div>
                            <h3>Score de Durabilité</h3>
                        </div>

                        <div class="environmental-metrics">
                            <div class="env-metric">
                                <div class="env-icon"><i class="fas fa-cloud"></i></div>
                                <div class="env-data">
                                    <div class="env-value" id="carbon-footprint">--- kg</div>
                                    <div class="env-label">Empreinte Carbone</div>
                                </div>
                            </div>
                            <div class="env-metric">
                                <div class="env-icon"><i class="fas fa-bolt"></i></div>
                                <div class="env-data">
                                    <div class="env-value" id="energy-class">-</div>
                                    <div class="env-label">Classe Énergétique</div>
                                </div>
                            </div>
                            <div class="env-metric">
                                <div class="env-icon"><i class="fas fa-sun"></i></div>
                                <div class="env-data">
                                    <div class="env-value" id="solar-potential">---%</div>
                                    <div class="env-label">Potentiel Solaire</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mission-controls">
                        <button class="control-btn primary" onclick="analyzeEnvironment()">
                            <i class="fas fa-leaf"></i>
                            Analyser l'Impact
                        </button>
                    </div>
                </div>
            `;
        }

        function createOptimizationInterface() {
            return `
                <div class="step-content">
                    <div class="step-header">
                        <h2 class="step-title">
                            <i class="fas fa-magic"></i>
                            Optimisation Automatique IA
                        </h2>
                        <p class="step-subtitle">Algorithmes génétiques pour l'optimisation multi-objectifs</p>
                    </div>

                    <div class="optimization-lab">
                        <div class="genetic-algorithm">
                            <h3>Algorithme Génétique en Action</h3>
                            <div class="generations-display">
                                <div class="generation" id="current-generation">
                                    <div class="generation-number">Génération: <span>0</span></div>
                                    <div class="fitness-score">Fitness: <span>0%</span></div>
                                </div>
                                <div class="evolution-progress">
                                    <div class="evolution-bar" id="evolution-progress"></div>
                                </div>
                            </div>
                        </div>

                        <div class="optimization-results">
                            <div class="pareto-solutions">
                                <h3>Solutions Pareto Optimales</h3>
                                <div class="solutions-grid" id="pareto-grid">
                                    <!-- Solutions seront générées ici -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mission-controls">
                        <button class="control-btn primary" onclick="startOptimization()">
                            <i class="fas fa-rocket"></i>
                            Optimiser avec IA
                        </button>
                    </div>
                </div>
            `;
        }

        function createReportInterface() {
            return `
                <div class="step-content">
                    <div class="step-header">
                        <h2 class="step-title">
                            <i class="fas fa-file-pdf"></i>
                            Rapport de Mission Complet
                        </h2>
                        <p class="step-subtitle">Génération automatique du rapport professionnel</p>
                    </div>

                    <div class="report-generator">
                        <div class="report-preview">
                            <div class="document-icon">
                                <i class="fas fa-file-pdf"></i>
                            </div>
                            <h3>Rapport d'Analyse BIM</h3>
                            <p>Document professionnel complet avec toutes les analyses effectuées</p>
                        </div>

                        <div class="report-sections">
                            <div class="section-item completed">
                                <i class="fas fa-check-circle"></i>
                                <span>Analyse Structurelle</span>
                            </div>
                            <div class="section-item completed">
                                <i class="fas fa-check-circle"></i>
                                <span>Détection d'Anomalies</span>
                            </div>
                            <div class="section-item completed">
                                <i class="fas fa-check-circle"></i>
                                <span>Classification IA</span>
                            </div>
                            <div class="section-item completed">
                                <i class="fas fa-check-circle"></i>
                                <span>Prédiction des Coûts</span>
                            </div>
                            <div class="section-item completed">
                                <i class="fas fa-check-circle"></i>
                                <span>Analyse Environnementale</span>
                            </div>
                            <div class="section-item completed">
                                <i class="fas fa-check-circle"></i>
                                <span>Optimisation IA</span>
                            </div>
                        </div>
                    </div>

                    <div class="mission-controls">
                        <button class="control-btn primary" onclick="generateReport()">
                            <i class="fas fa-download"></i>
                            Générer le Rapport
                        </button>
                        <button class="control-btn secondary" onclick="previewReport()">
                            <i class="fas fa-eye"></i>
                            Aperçu
                        </button>
                    </div>
                </div>
            `;
        }

        // Fonctions d'interaction pour les différentes étapes
        function startMission() {
            if (!selectedProject) {
                alert('⚠️ Veuillez sélectionner un projet avant de commencer !');
                return;
            }

            console.log(`🚀 Mission démarrée pour le projet: ${selectedProject}`);
            markStepCompleted('upload');
            switchToStep('analyze');

            // Démarrer automatiquement l'analyse
            setTimeout(() => {
                startAnalysis();
            }, 1000);
        }

        function loadDemo() {
            console.log('👁️ Mode démonstration activé');
            // Simuler le chargement d'un projet de démonstration
            setTimeout(() => {
                startMission();
            }, 1000);
        }

        async function startAnalysis() {
            if (!selectedProject) return;

            console.log('🔍 Analyse démarrée pour:', selectedProject);

            try {
                // Lancer l'analyse réelle
                const response = await fetch(`http://localhost:8000/analyze/${selectedProject}`);
                const analysisData = await response.json();

                // Mettre à jour les métriques avec les vraies données
                updateAnalysisMetrics(analysisData);

                // Simuler la progression
                simulateProgress('analysis-progress', () => {
                    markStepCompleted('analyze');
                    switchToStep('anomalies');
                });

            } catch (error) {
                console.error('Erreur lors de l\'analyse:', error);
                // Fallback vers simulation
                simulateProgress('analysis-progress', () => {
                    markStepCompleted('analyze');
                    switchToStep('anomalies');
                });
            }
        }

        // Mettre à jour les métriques d'analyse avec les vraies données
        function updateAnalysisMetrics(data) {
            if (data) {
                const totalElements = document.getElementById('total-elements');
                const totalArea = document.getElementById('total-area');
                const totalVolume = document.getElementById('total-volume');
                const totalMaterials = document.getElementById('total-materials');

                if (totalElements) totalElements.textContent = data.total_elements || '---';
                if (totalArea) totalArea.textContent = data.total_floor_area ? Math.round(data.total_floor_area) + ' m²' : '---';
                if (totalVolume) totalVolume.textContent = data.total_volume ? Math.round(data.total_volume) + ' m³' : '---';
                if (totalMaterials) totalMaterials.textContent = data.materials_count || '---';
            }
        }

        async function startAnomalyDetection() {
            if (!selectedProject) return;

            console.log('🛡️ Détection d\'anomalies démarrée pour:', selectedProject);

            try {
                // Lancer la détection d'anomalies réelle
                const response = await fetch(`http://localhost:8000/detect-anomalies/${selectedProject}`);
                const anomaliesData = await response.json();

                // Mettre à jour les compteurs avec les vraies données
                updateAnomaliesCount(anomaliesData);

                setTimeout(() => {
                    markStepCompleted('anomalies');
                    switchToStep('classification');
                }, 3000);

            } catch (error) {
                console.error('Erreur lors de la détection d\'anomalies:', error);
                // Fallback vers simulation
                simulateAnomalyScanning(() => {
                    markStepCompleted('anomalies');
                    switchToStep('classification');
                });
            }
        }

        // Mettre à jour les compteurs d'anomalies avec les vraies données
        function updateAnomaliesCount(data) {
            if (data && data.anomalies_by_severity) {
                const criticalCount = document.getElementById('critical-count');
                const highCount = document.getElementById('high-count');
                const mediumCount = document.getElementById('medium-count');
                const lowCount = document.getElementById('low-count');

                if (criticalCount) criticalCount.textContent = data.anomalies_by_severity.critical || 0;
                if (highCount) highCount.textContent = data.anomalies_by_severity.high || 0;
                if (mediumCount) mediumCount.textContent = data.anomalies_by_severity.medium || 0;
                if (lowCount) lowCount.textContent = data.anomalies_by_severity.low || 0;
            }
        }

        function startClassification() {
            console.log('🧠 Classification IA démarrée');
            simulateNeuralNetwork(() => {
                markStepCompleted('classification');
                switchToStep('costs');
            });
        }

        function predictCosts() {
            console.log('💰 Prédiction des coûts démarrée');
            simulateCostCalculation(() => {
                markStepCompleted('costs');
                switchToStep('environment');
            });
        }

        function analyzeEnvironment() {
            console.log('🌱 Analyse environnementale démarrée');
            simulateEnvironmentalAnalysis(() => {
                markStepCompleted('environment');
                switchToStep('optimization');
            });
        }

        function startOptimization() {
            console.log('⚡ Optimisation IA démarrée');
            simulateGeneticAlgorithm(() => {
                markStepCompleted('optimization');
                switchToStep('report');
            });
        }

        function generateReport() {
            console.log('📄 Génération du rapport');
            markStepCompleted('report');
            // Rediriger vers la génération de rapport réelle
            window.location.href = '/generate-html-report?project=demo&auto=true';
        }

        function previewReport() {
            console.log('👁️ Aperçu du rapport');
            // Ouvrir l'aperçu dans une nouvelle fenêtre
            window.open('/generate-html-report?project=demo&preview=true', '_blank');
        }

        // Fonctions utilitaires
        function markStepCompleted(step) {
            const button = document.querySelector(`[data-step="${step}"]`);
            if (button) {
                button.classList.add('completed');
                button.classList.remove('active');
            }
        }

        function simulateProgress(elementId, callback) {
            const progressBar = document.getElementById(elementId);
            const statusElement = document.getElementById('analysis-status');
            const percentageElement = document.querySelector('.progress-percentage');

            if (!progressBar) return;

            let progress = 0;
            const interval = setInterval(() => {
                progress += Math.random() * 15;
                if (progress > 100) progress = 100;

                progressBar.style.width = progress + '%';
                if (percentageElement) percentageElement.textContent = Math.round(progress) + '%';

                // Messages de statut dynamiques
                if (statusElement) {
                    const messages = [
                        'Extraction des éléments IFC...',
                        'Analyse des géométries...',
                        'Calcul des surfaces et volumes...',
                        'Identification des matériaux...',
                        'Finalisation de l\'analyse...'
                    ];
                    const messageIndex = Math.floor((progress / 100) * messages.length);
                    statusElement.textContent = messages[Math.min(messageIndex, messages.length - 1)];
                }

                if (progress >= 100) {
                    clearInterval(interval);
                    setTimeout(callback, 500);
                }
            }, 200);
        }

        function simulateAnomalyScanning(callback) {
            // Simuler la détection d'anomalies avec animation radar
            const counts = {
                critical: Math.floor(Math.random() * 5),
                high: Math.floor(Math.random() * 10) + 2,
                medium: Math.floor(Math.random() * 15) + 5,
                low: Math.floor(Math.random() * 20) + 10
            };

            setTimeout(() => {
                document.getElementById('critical-count').textContent = counts.critical;
                document.getElementById('high-count').textContent = counts.high;
                document.getElementById('medium-count').textContent = counts.medium;
                document.getElementById('low-count').textContent = counts.low;

                setTimeout(callback, 2000);
            }, 3000);
        }

        function simulateNeuralNetwork(callback) {
            // Animer le réseau de neurones
            const neurons = document.querySelectorAll('.neuron');
            let index = 0;

            const activateNeuron = () => {
                if (index < neurons.length) {
                    neurons[index].classList.add('active');
                    index++;
                    setTimeout(activateNeuron, 300);
                } else {
                    // Afficher le résultat de classification
                    setTimeout(() => {
                        const buildingTypes = ['Résidentiel', 'Tertiaire', 'Industriel', 'Commercial'];
                        const selectedType = buildingTypes[Math.floor(Math.random() * buildingTypes.length)];
                        const confidence = Math.floor(Math.random() * 30) + 70; // 70-100%

                        document.getElementById('building-type').textContent = selectedType;
                        document.getElementById('confidence-bar').style.width = confidence + '%';
                        document.getElementById('confidence-text').textContent = confidence + '%';

                        setTimeout(callback, 2000);
                    }, 1000);
                }
            };

            activateNeuron();
        }

        function simulateCostCalculation(callback) {
            // Simuler le calcul des coûts
            const totalCost = Math.floor(Math.random() * 500000) + 200000; // 200k-700k€
            const costPerM2 = Math.floor(totalCost / (Math.random() * 200 + 100)); // Surface aléatoire
            const confidence = Math.floor(Math.random() * 20) + 80; // 80-100%

            setTimeout(() => {
                document.getElementById('total-cost').textContent = '€ ' + totalCost.toLocaleString('fr-FR');
                document.getElementById('cost-per-m2').textContent = '€ ' + costPerM2.toLocaleString('fr-FR');
                document.getElementById('cost-confidence').textContent = confidence + '%';

                setTimeout(callback, 2000);
            }, 2000);
        }

        function simulateEnvironmentalAnalysis(callback) {
            // Simuler l'analyse environnementale
            const sustainabilityScore = Math.floor(Math.random() * 4) + 6; // 6-10
            const carbonFootprint = Math.floor(Math.random() * 50000) + 10000; // 10-60 tonnes
            const energyClass = ['A+', 'A', 'B', 'C'][Math.floor(Math.random() * 4)];
            const solarPotential = Math.floor(Math.random() * 40) + 60; // 60-100%

            setTimeout(() => {
                // Animer le score circulaire
                const scoreProgress = document.getElementById('score-progress');
                const circumference = 2 * Math.PI * 50; // rayon = 50
                const offset = circumference - (sustainabilityScore / 10) * circumference;
                scoreProgress.style.strokeDashoffset = offset;

                document.getElementById('sustainability-score').textContent = sustainabilityScore;
                document.getElementById('carbon-footprint').textContent = (carbonFootprint / 1000).toFixed(1) + ' t';
                document.getElementById('energy-class').textContent = energyClass;
                document.getElementById('solar-potential').textContent = solarPotential + '%';

                setTimeout(callback, 3000);
            }, 2000);
        }

        function simulateGeneticAlgorithm(callback) {
            // Simuler l'algorithme génétique
            let generation = 0;
            const maxGenerations = 50;

            const evolve = () => {
                if (generation < maxGenerations) {
                    generation++;
                    const fitness = Math.min(100, generation * 2 + Math.random() * 10);

                    document.querySelector('.generation-number span').textContent = generation;
                    document.querySelector('.fitness-score span').textContent = Math.round(fitness) + '%';
                    document.getElementById('evolution-progress').style.width = (generation / maxGenerations * 100) + '%';

                    setTimeout(evolve, 100);
                } else {
                    setTimeout(callback, 1000);
                }
            };

            evolve();
        }

        // Variables globales
        let selectedProject = null;
        let projectData = null;

        // Charger les projets disponibles
        async function loadAvailableProjects() {
            try {
                const response = await fetch('http://localhost:8000/projects');
                const projects = await response.json();

                const select = document.getElementById('project-select');
                if (select) {
                    select.innerHTML = '<option value="">-- Choisir un projet --</option>';

                    projects.forEach(project => {
                        const option = document.createElement('option');
                        option.value = project.id;
                        option.textContent = `${project.name} (${project.id})`;
                        select.appendChild(option);
                    });
                }
            } catch (error) {
                console.error('Erreur lors du chargement des projets:', error);
            }
        }

        // Gestion de la sélection de projet
        function onProjectSelected() {
            const select = document.getElementById('project-select');
            const startBtn = document.getElementById('start-mission-btn');

            if (select.value) {
                selectedProject = select.value;
                startBtn.disabled = false;
                startBtn.innerHTML = '<i class="fas fa-rocket"></i> Analyser ' + selectedProject;

                // Charger les données du projet
                loadProjectData(selectedProject);
            } else {
                selectedProject = null;
                startBtn.disabled = true;
                startBtn.innerHTML = '<i class="fas fa-play"></i> Démarrer l\'Analyse IA';
            }
        }

        // Charger les données du projet sélectionné
        async function loadProjectData(projectId) {
            try {
                const response = await fetch(`http://localhost:8000/project-info/${projectId}`);
                projectData = await response.json();

                // Mettre à jour le panel d'informations
                updateProjectStatus(projectData);

            } catch (error) {
                console.error('Erreur lors du chargement des données:', error);
            }
        }

        // Mettre à jour le statut du projet
        function updateProjectStatus(data) {
            const statusPanel = document.getElementById('project-status');
            if (statusPanel && data) {
                statusPanel.innerHTML = `
                    <div class="metric-item">
                        <span class="metric-name">Projet</span>
                        <span class="metric-value">${data.name || selectedProject}</span>
                    </div>
                    <div class="metric-item">
                        <span class="metric-name">Éléments</span>
                        <span class="metric-value">${data.total_elements || 'N/A'}</span>
                    </div>
                    <div class="metric-item">
                        <span class="metric-name">Surface</span>
                        <span class="metric-value">${data.total_floor_area ? Math.round(data.total_floor_area) + ' m²' : 'N/A'}</span>
                    </div>
                    <div class="metric-item">
                        <span class="metric-name">Statut</span>
                        <span class="metric-value">Prêt</span>
                    </div>
                `;
            }
        }

        // Toggle mode upload
        function toggleUploadMode() {
            const uploadZone = document.getElementById('upload-zone');
            const projectSelector = document.querySelector('.project-selector-main');

            if (uploadZone.style.display === 'none') {
                uploadZone.style.display = 'block';
                projectSelector.style.display = 'none';
            } else {
                uploadZone.style.display = 'none';
                projectSelector.style.display = 'block';
            }
        }

        // Initialiser avec la première étape
        loadStepContent('upload');

        // Charger les projets au démarrage
        setTimeout(() => {
            loadAvailableProjects();
        }, 1000);

        // Mise à jour des métriques temps réel dans le panel d'informations
        function updateRealTimeMetrics() {
            const metricsPanel = document.getElementById('real-time-metrics');
            if (metricsPanel) {
                metricsPanel.innerHTML = `
                    <div class="metric-item">
                        <span class="metric-name">CPU IA</span>
                        <span class="metric-value">${Math.floor(Math.random() * 30) + 20}%</span>
                    </div>
                    <div class="metric-item">
                        <span class="metric-name">Mémoire</span>
                        <span class="metric-value">${Math.floor(Math.random() * 40) + 30}%</span>
                    </div>
                    <div class="metric-item">
                        <span class="metric-name">Réseau</span>
                        <span class="metric-value">${Math.floor(Math.random() * 100) + 50} MB/s</span>
                    </div>
                `;
            }
        }

        // Fonctions pour afficher les pop-ups d'analyse
        async function showDetailedAnalysis() {
            if (!selectedProject) {
                alert('⚠️ Veuillez sélectionner un projet d\'abord !');
                return;
            }

            try {
                const response = await fetch(`http://localhost:8000/detailed-analysis/${selectedProject}`);
                const data = await response.json();

                // Créer et afficher le pop-up
                createAnalysisPopup('Analyse Détaillée', data, 'detailed');

            } catch (error) {
                console.error('Erreur:', error);
                alert('❌ Erreur lors de l\'analyse détaillée');
            }
        }

        async function showPMRAnalysis() {
            if (!selectedProject) {
                alert('⚠️ Veuillez sélectionner un projet d\'abord !');
                return;
            }

            try {
                const response = await fetch(`http://localhost:8000/pmr-analysis/${selectedProject}`);
                const data = await response.json();

                createAnalysisPopup('Analyse PMR', data, 'pmr');

            } catch (error) {
                console.error('Erreur:', error);
                alert('❌ Erreur lors de l\'analyse PMR');
            }
        }

        async function showClassification() {
            if (!selectedProject) {
                alert('⚠️ Veuillez sélectionner un projet d\'abord !');
                return;
            }

            try {
                const response = await fetch(`http://localhost:8000/classify/${selectedProject}`);
                const data = await response.json();

                createAnalysisPopup('Classification IA', data, 'classification');

            } catch (error) {
                console.error('Erreur:', error);
                alert('❌ Erreur lors de la classification');
            }
        }

        // Créer un pop-up d'analyse futuriste
        function createAnalysisPopup(title, data, type) {
            const popup = document.createElement('div');
            popup.className = 'analysis-popup';
            popup.innerHTML = `
                <div class="popup-overlay" onclick="closeAnalysisPopup()"></div>
                <div class="popup-content">
                    <div class="popup-header">
                        <h3><i class="fas fa-chart-line"></i> ${title}</h3>
                        <button class="popup-close" onclick="closeAnalysisPopup()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="popup-body">
                        ${formatAnalysisData(data, type)}
                    </div>
                </div>
            `;

            document.body.appendChild(popup);

            // Animation d'entrée
            setTimeout(() => {
                popup.classList.add('active');
            }, 10);
        }

        // Formater les données selon le type d'analyse
        function formatAnalysisData(data, type) {
            if (!data) return '<p>Aucune donnée disponible</p>';

            switch (type) {
                case 'detailed':
                    return `
                        <div class="analysis-grid">
                            <div class="analysis-card">
                                <h4>📊 Éléments BIM</h4>
                                <div class="metric-large">${data.total_elements || 'N/A'}</div>
                            </div>
                            <div class="analysis-card">
                                <h4>📐 Surface Totale</h4>
                                <div class="metric-large">${data.total_floor_area ? Math.round(data.total_floor_area) + ' m²' : 'N/A'}</div>
                            </div>
                            <div class="analysis-card">
                                <h4>📦 Volume</h4>
                                <div class="metric-large">${data.total_volume ? Math.round(data.total_volume) + ' m³' : 'N/A'}</div>
                            </div>
                        </div>
                    `;

                case 'pmr':
                    return `
                        <div class="pmr-analysis">
                            <div class="compliance-score">
                                <h4>🦽 Score de Conformité PMR</h4>
                                <div class="score-circle">
                                    <span class="score-value">${data.compliance_rate ? Math.round(data.compliance_rate) : 'N/A'}%</span>
                                </div>
                            </div>
                            <div class="pmr-details">
                                <h4>Détails de l'Analyse</h4>
                                <p>Analyse de l'accessibilité selon les normes PMR en cours...</p>
                            </div>
                        </div>
                    `;

                case 'classification':
                    return `
                        <div class="classification-result">
                            <h4>🧠 Classification par IA</h4>
                            <div class="building-type-result">
                                <span class="type-label">Type de Bâtiment:</span>
                                <span class="type-value">${data.building_type || 'Non classifié'}</span>
                            </div>
                            <div class="confidence-bar">
                                <span>Confiance: ${data.confidence ? Math.round(data.confidence * 100) : 0}%</span>
                                <div class="bar">
                                    <div class="fill" style="width: ${data.confidence ? data.confidence * 100 : 0}%"></div>
                                </div>
                            </div>
                        </div>
                    `;

                default:
                    return '<p>Données d\'analyse en cours de traitement...</p>';
            }
        }

        // Fermer le pop-up
        function closeAnalysisPopup() {
            const popup = document.querySelector('.analysis-popup');
            if (popup) {
                popup.classList.remove('active');
                setTimeout(() => popup.remove(), 300);
            }
        }

        // Mettre à jour les métriques toutes les 3 secondes
        setInterval(updateRealTimeMetrics, 3000);
        updateRealTimeMetrics();
    </script>
</body>
</html>
