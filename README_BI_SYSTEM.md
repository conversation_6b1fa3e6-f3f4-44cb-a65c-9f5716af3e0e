# 🚀 SYSTÈME BUSINESS INTELLIGENCE BIMEX

## 🎯 DÉMARRAGE ULTRA-RAPIDE (1 CLIC)

### Windows
```bash
setup_and_run.bat
```

### Linux/Mac
```bash
chmod +x setup_and_run.sh
./setup_and_run.sh
```

## 📋 DÉMARRAGE MANUEL (ÉTAPE PAR ÉTAPE)

### 1. Configuration automatique
```bash
python setup_bi_system.py
```

### 2. Démarrage du serveur
```bash
python start_bimex_bi.py
```

### 3. Accès à l'interface
- **URL**: http://localhost:8000/app/home.html
- **Cliquez sur le bouton BI flottant** (coin supérieur droit)

## 🧪 TESTS AUTOMATIQUES

Vérifiez que tout fonctionne :
```bash
python test_bi_system.py
```

## 📊 FONCTIONNALITÉS DISPONIBLES

### Dashboard BI Complet
- 🟡 **Power BI Integration** - Export automatique vers Power BI
- 🔵 **Tableau Analytics** - Publication vers Tableau Server
- 🔴 **n8n Automation** - Workflows automatisés
- 🟢 **ERP Connector** - Synchronisation SAP/Oracle/Dynamics

### Actions Rapides
- **Export Multi-Plateformes** - Exporte vers toutes les plateformes
- **Historique** - Voir toutes les synchronisations
- **Statut BI** - Vérifier l'état des connecteurs

### APIs Disponibles
- `GET /bi/status` - Statut des connecteurs
- `POST /bi/export-powerbi` - Export Power BI
- `POST /bi/export-tableau` - Export Tableau
- `POST /bi/trigger-n8n-workflow` - Workflows n8n
- `POST /bi/sync-erp` - Synchronisation ERP
- `GET /bi/sync-history` - Historique des syncs

## 🔧 CONFIGURATION PERSONNALISÉE

### Modifier les connecteurs
Éditez `backend/bi_config.json` :

```json
{
  "connectors": [
    {
      "name": "PowerBI_Production",
      "type": "powerbi",
      "endpoint": "https://api.powerbi.com/v1.0/myorg/datasets",
      "credentials": {
        "client_id": "VOTRE_CLIENT_ID",
        "client_secret": "VOTRE_CLIENT_SECRET", 
        "tenant_id": "VOTRE_TENANT_ID"
      },
      "active": true
    }
  ]
}
```

### Projets BIM supportés
Le système détecte automatiquement vos projets dans :
- `xeokit-bim-viewer/app/data/projects/`

## 🎮 UTILISATION

### 1. Interface Principale
- Ouvrez http://localhost:8000/app/home.html
- Le dashboard principal s'affiche avec vos projets BIM

### 2. Dashboard BI
- Cliquez sur le **bouton BI flottant** (coin supérieur droit)
- Le dashboard BI s'ouvre en plein écran

### 3. Widgets Interactifs
- **Cliquez sur un widget** pour voir les détails
- **Utilisez les boutons d'action** pour synchroniser
- **Consultez les métriques** en temps réel

### 4. Export de Données
- **Synchroniser** : Export vers une plateforme spécifique
- **Export Multi-Plateformes** : Export vers toutes les plateformes
- **Historique** : Voir toutes les synchronisations passées

## 🛠️ DÉPANNAGE

### Problèmes courants

#### Port 8000 occupé
```python
# Dans backend/main.py, ligne finale :
uvicorn.run(app, host="0.0.0.0", port=8001)  # Changez le port
```

#### Erreur de module
```bash
pip install fastapi uvicorn python-multipart requests pandas
```

#### Projet non trouvé
- Vérifiez que vos projets sont dans `xeokit-bim-viewer/app/data/projects/`
- Le système cherche les fichiers `geometry.ifc`

### Logs et Debug
- Les logs s'affichent dans la console du serveur
- Utilisez `python test_bi_system.py` pour diagnostiquer

## 📈 MÉTRIQUES ET DONNÉES

### Données extraites automatiquement
- **Éléments BIM** : Murs, dalles, poutres, colonnes, etc.
- **Espaces** : Pièces, zones, étages
- **Matériaux** : Types et quantités
- **Métriques** : Surface, volume, nombre d'éléments
- **Qualité** : Complétude, richesse des données, précision

### Formats d'export
- **Power BI** : Datasets structurés avec tables relationnelles
- **Tableau** : Fichiers CSV optimisés pour visualisation
- **n8n** : JSON pour workflows automatisés
- **ERP** : Données de coûts et quantités

## 🚀 FONCTIONNALITÉS AVANCÉES

### Workflows Automatisés
- Export quotidien automatique
- Alertes sur anomalies de données
- Synchronisation ERP temps réel
- Rapports automatisés par email

### Intégration ERP
- Synchronisation des coûts de projet
- Données de quantités et matériaux
- Intégration SAP, Oracle, Dynamics

### APIs RESTful
- Intégration facile avec autres systèmes
- Webhooks pour notifications
- Format JSON standardisé

## 📞 SUPPORT

### Vérifications de base
1. **Serveur démarré** : Console doit afficher "Uvicorn running"
2. **Port accessible** : http://localhost:8000 doit répondre
3. **Configuration** : `backend/bi_config.json` doit exister

### Tests de diagnostic
```bash
python test_bi_system.py
```

### Structure des fichiers
```
├── backend/
│   ├── main.py                 # Serveur principal
│   ├── bi_integration.py       # Module BI
│   └── bi_config.json         # Configuration
├── xeokit-bim-viewer/
│   └── app/
│       ├── home.html          # Interface principale
│       └── data/projects/     # Projets BIM
├── setup_bi_system.py         # Configuration auto
├── start_bimex_bi.py          # Démarrage
└── test_bi_system.py          # Tests
```

## 🎉 FÉLICITATIONS !

Votre système Business Intelligence BIMEX est maintenant opérationnel !

**Prochaines étapes** :
1. Testez avec vos propres projets BIM
2. Configurez vos vraies APIs (Power BI, Tableau, etc.)
3. Créez des workflows automatisés
4. Intégrez avec vos systèmes ERP

**Bon travail !** 🚀✨
