{"connectors": [{"name": "PowerBI_Production", "type": "powerbi", "endpoint": "https://api.powerbi.com/v1.0/myorg/datasets", "credentials": {"client_id": "your_powerbi_client_id", "client_secret": "your_powerbi_client_secret", "tenant_id": "your_tenant_id"}, "active": true, "last_sync": null}, {"name": "Tableau_Server", "type": "tableau", "endpoint": "https://your-tableau-server.com/api/3.0", "credentials": {"username": "your_tableau_username", "password": "your_tableau_password", "site_id": "your_site_id"}, "active": true, "last_sync": null}, {"name": "n8n_Workflows", "type": "n8n", "endpoint": "https://your-n8n-instance.com/webhook", "credentials": {"webhook_id": "your_webhook_id", "api_key": "your_n8n_api_key"}, "active": true, "last_sync": null}, {"name": "ERP_SAP", "type": "erp", "endpoint": "https://your-sap-system.com/api", "credentials": {"username": "sap_user", "password": "sap_password", "client": "100"}, "active": true, "last_sync": null}]}