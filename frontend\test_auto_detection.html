<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test - Détection Automatique BIMEX 2.0</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-neon: #00f5ff;
            --secondary-neon: #ff0080;
            --success-neon: #00ff88;
            --bg-dark: #0a0a0f;
            --bg-card: #1a1a2e;
            --text-primary: #ffffff;
            --text-secondary: #a0a0b0;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: var(--bg-dark);
            color: var(--text-primary);
            padding: 40px;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .test-container {
            background: var(--bg-card);
            border-radius: 20px;
            padding: 40px;
            max-width: 800px;
            width: 100%;
            border: 1px solid rgba(0, 245, 255, 0.2);
        }

        .test-title {
            text-align: center;
            font-size: 32px;
            margin-bottom: 30px;
            background: linear-gradient(135deg, var(--primary-neon), var(--secondary-neon));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(0, 245, 255, 0.05);
            border-radius: 12px;
            border: 1px solid rgba(0, 245, 255, 0.1);
        }

        .test-section h3 {
            color: var(--primary-neon);
            margin-bottom: 15px;
        }

        .test-link {
            display: inline-block;
            background: linear-gradient(135deg, var(--primary-neon), var(--secondary-neon));
            color: white;
            padding: 15px 25px;
            border-radius: 12px;
            text-decoration: none;
            margin: 10px;
            transition: all 0.3s ease;
            font-weight: 600;
        }

        .test-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 0 20px rgba(0, 245, 255, 0.5);
        }

        .test-link.secondary {
            background: transparent;
            border: 2px solid var(--primary-neon);
        }

        .test-link.secondary:hover {
            background: rgba(0, 245, 255, 0.1);
        }

        .code-block {
            background: #000;
            color: #00ff00;
            padding: 15px;
            border-radius: 8px;
            font-family: 'JetBrains Mono', monospace;
            font-size: 12px;
            margin: 10px 0;
            overflow-x: auto;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-success { background: var(--success-neon); }
        .status-warning { background: #ffaa00; }
        .status-error { background: #ff3366; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">🧪 Test - Détection Automatique BIMEX 2.0</h1>
        
        <div class="test-section">
            <h3><i class="fas fa-rocket"></i> Tests de Détection Automatique</h3>
            <p>Testez la détection automatique de fichiers avec différents projets :</p>
            
            <div style="margin: 20px 0;">
                <a href="bim_analysis_v2.html?project=BasicHouse&auto=true&file_detected=true&step=detailed" class="test-link">
                    <i class="fas fa-home"></i> Test BasicHouse
                </a>
                
                <a href="bim_analysis_v2.html?project=SampleProject&auto=true&file_detected=true&step=detailed" class="test-link">
                    <i class="fas fa-building"></i> Test SampleProject
                </a>
                
                <a href="bim_analysis_v2.html?project=TestModel&auto=true&file_detected=true&step=detailed" class="test-link">
                    <i class="fas fa-cube"></i> Test TestModel
                </a>
            </div>
        </div>

        <div class="test-section">
            <h3><i class="fas fa-cog"></i> Tests de Mode Manuel</h3>
            <p>Testez le mode de sélection manuelle :</p>
            
            <div style="margin: 20px 0;">
                <a href="bim_analysis_v2.html" class="test-link secondary">
                    <i class="fas fa-hand-pointer"></i> Mode Sélection Manuelle
                </a>
            </div>
        </div>

        <div class="test-section">
            <h3><i class="fas fa-info-circle"></i> Paramètres URL Attendus</h3>
            <p>Format des paramètres pour la détection automatique :</p>
            
            <div class="code-block">
bim_analysis_v2.html?project=NOM_PROJET&auto=true&file_detected=true&step=detailed
            </div>
            
            <p><strong>Paramètres :</strong></p>
            <ul style="color: var(--text-secondary); margin-left: 20px;">
                <li><code>project</code> : Nom du projet BIM</li>
                <li><code>auto=true</code> : Active le mode automatique</li>
                <li><code>file_detected=true</code> : Indique qu'un fichier est détecté</li>
                <li><code>step=detailed</code> : Étape d'analyse détaillée</li>
            </ul>
        </div>

        <div class="test-section">
            <h3><i class="fas fa-check-circle"></i> Vérifications Attendues</h3>
            <p>Lors du test, vérifiez que :</p>
            
            <div style="margin: 15px 0;">
                <div><span class="status-indicator status-success"></span>Le fichier est détecté automatiquement</div>
                <div><span class="status-indicator status-success"></span>Les informations du projet s'affichent</div>
                <div><span class="status-indicator status-success"></span>Le bouton "Démarrer l'Analyse IA" est actif</div>
                <div><span class="status-indicator status-success"></span>Le panel de droite affiche les métadonnées</div>
                <div><span class="status-indicator status-success"></span>Les actions rapides fonctionnent</div>
            </div>
        </div>

        <div class="test-section">
            <h3><i class="fas fa-compare"></i> Comparaison avec l'Ancienne Version</h3>
            <p>Comparez avec l'interface originale :</p>
            
            <div style="margin: 20px 0;">
                <a href="../frontend/bim_analysis.html?project=BasicHouse&auto=true&file_detected=true&step=detailed" class="test-link secondary">
                    <i class="fas fa-eye"></i> Ancienne Interface (bim_analysis.html)
                </a>
            </div>
        </div>

        <div class="test-section">
            <h3><i class="fas fa-home"></i> Retour à l'Accueil</h3>
            <p>Testez le workflow complet depuis la page d'accueil :</p>
            
            <div style="margin: 20px 0;">
                <a href="../xeokit-bim-viewer/app/home.html" class="test-link">
                    <i class="fas fa-arrow-left"></i> Page d'Accueil BIMEX 2.0
                </a>
            </div>
        </div>
    </div>

    <script>
        // Afficher les paramètres URL actuels
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.toString()) {
            console.log('🔍 Paramètres URL détectés:', Object.fromEntries(urlParams));
        }

        // Fonction pour tester la détection
        function testAutoDetection(project) {
            const url = `bim_analysis_v2.html?project=${project}&auto=true&file_detected=true&step=detailed`;
            console.log(`🧪 Test de détection pour ${project}:`, url);
            window.open(url, '_blank');
        }

        // Ajouter des raccourcis clavier pour les tests rapides
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case '1':
                        e.preventDefault();
                        testAutoDetection('BasicHouse');
                        break;
                    case '2':
                        e.preventDefault();
                        testAutoDetection('SampleProject');
                        break;
                    case '3':
                        e.preventDefault();
                        testAutoDetection('TestModel');
                        break;
                    case '0':
                        e.preventDefault();
                        window.open('bim_analysis_v2.html', '_blank');
                        break;
                }
            }
        });

        console.log('🎮 Raccourcis clavier disponibles:');
        console.log('Ctrl+1 : Test BasicHouse');
        console.log('Ctrl+2 : Test SampleProject');
        console.log('Ctrl+3 : Test TestModel');
        console.log('Ctrl+0 : Mode manuel');
    </script>
</body>
</html>
